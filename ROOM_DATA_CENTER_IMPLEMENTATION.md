# 房间数据中心功能实现文档

## 概述
本文档描述了在 `RoomDataCenterService.java` 中实现的房间数据中心功能，包括数据收集、处理和展示。

## 实现的功能

### 1. CacheDataService.getRoomDataCenterVODetailInfo 方法
**位置**: `src/main/java/com/quhong/service/CacheDataService.java`

**功能**: 从Redis缓存中获取房间数据中心的详细信息，如果不存在则创建新的实例。

**实现要点**:
- 使用 `@Cacheable` 注解进行缓存管理，缓存时间为1分钟
- 从Redis Hash中获取JSON数据并反序列化为 `RoomDataCenterVO.DetailInfo` 对象
- 初始化所有Set集合字段，避免空指针异常
- 如果缓存中没有数据，创建新的实例并初始化所有字段

**代码示例**:
```java
@Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
        key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
public RoomDataCenterVO.DetailInfo getRoomDataCenterVODetailInfo(String detailKey, String dayStr, String roomId) {
    // 实现逻辑...
}
```

### 2. syncAddHandle 方法中的数据收集
**位置**: `src/main/java/com/quhong/service/RoomDataCenterService.java`

**功能**: 在syncAddHandle方法中完成对 `RoomDataCenterVO.DetailInfo` 的数据收集。

**处理的事件类型**:

#### 2.1 上麦时长事件 (ON_MIC_TIME)
- 统计用户上麦1分钟的人数
- 区分新用户和所有用户
- 如果在房间活动期间，同时统计活动期间数据

#### 2.2 邀请上麦事件 (INVITE_USER_ON_MIC_ALL)
- 统计邀请用户上麦的人数
- 区分新用户和所有用户

#### 2.3 加入房间事件 (JOIN_ROOM_MEMBER)
- 统计用户加入房间的人数
- 区分新用户和所有用户

#### 2.4 送礼事件处理
- 统计送礼用户数量
- 统计钻石消耗总数
- 区分新用户和所有用户的消耗
- 如果在房间活动期间，同时统计活动期间数据

#### 2.5 最大在线人数处理
- 更新房间最大在线人数

**关键实现**:
```java
// 判断是否是新用户
boolean isNewUser = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());

// 判断是否在房间活动期间
boolean isRoomEvent = cacheDataService.isRunRoomEvent(roomId) != null;
```

### 3. roomDataCenterInfo 方法中的数据填充
**位置**: `src/main/java/com/quhong/service/RoomDataCenterService.java`

**功能**: 在roomDataCenterInfo方法中完成 `RoomDataCenterVO.DetailInfoVO` 数据的填充。

**数据转换逻辑**:
- 从 `DetailInfo` 的Set集合转换为 `DetailInfoVO` 的计数字段
- 直接复制数值型字段
- 确保所有字段都有合理的默认值

**填充的数据类型**:

#### 3.1 房间活跃数据
- `roomOwnerTime`: 房主在线时长（分钟）
- `roomMaxOnlineCount`: 房间最大在线人数
- `inviteUserCount`: 邀请用户上麦人数
- `inviteNewUserCount`: 邀请新用户上麦人数
- `allUserMicCount`: 用户上麦1分钟人数
- `newUserMicCount`: 新用户上麦1分钟人数
- `allUserJoinRoomCount`: 用户加入房间人数
- `newUserJoinRoomCount`: 新用户加入房间人数

#### 3.2 房间消耗数据
- `sendGiftCount`: 送礼人数
- `allSendGiftDiamondCount`: 所有用户送礼消耗钻石数
- `newSendGiftDiamondCount`: 新用户送礼消耗钻石数

#### 3.3 房间活动数据
- `sendGiftEventCount`: 房间活动期间-送礼人数
- `allSendGiftDiamondEventCount`: 房间活动期间-所有用户送礼消耗钻石数
- `allUserMicEventCount`: 房间活动期间-用户上麦1分钟人数
- `newUserMicEventCount`: 房间活动期间-新用户上麦1分钟人数

### 4. 核心工具方法的使用

#### 4.1 ActorUtils.isNewDeviceAccount
**功能**: 判断是否是新设备账号
**参数**: 
- `uid`: 用户ID
- `firstTnId`: 首次设备ID

**使用场景**: 在所有数据统计中区分新用户和老用户

#### 4.2 cacheDataService.isRunRoomEvent
**功能**: 判断是否在房间活动期间
**参数**: 
- `roomId`: 房间ID
**返回值**: 活动ID字符串（活动期间）或null（非活动期间）

**使用场景**: 统计房间活动期间的特殊数据

## 数据流程

1. **数据收集阶段**: 通过MQ消息或直接调用，触发 `syncAddHandle` 方法
2. **数据处理阶段**: 根据事件类型和用户类型，更新对应的统计数据
3. **数据存储阶段**: 将更新后的数据序列化为JSON并存储到Redis
4. **数据展示阶段**: 通过 `roomDataCenterInfo` 方法获取数据并转换为前端需要的格式

## 技术特点

1. **线程安全**: 使用 `synchronized` 和 `stringPool.intern()` 确保并发安全
2. **缓存优化**: 使用Caffeine缓存减少Redis访问
3. **数据一致性**: 通过事务性操作确保数据一致性
4. **性能优化**: 使用Set集合去重，避免重复统计
5. **扩展性**: 易于添加新的统计维度和事件类型

## 测试

创建了完整的单元测试 `RoomDataCenterServiceTest.java`，覆盖了：
- 数据填充逻辑测试
- 工具方法使用测试
- 数据处理逻辑测试
- 边界条件测试

## 注意事项

1. 确保所有Set集合在使用前都已初始化
2. 新用户判断依赖于 `ActorData.firstTnId` 字段
3. 房间活动判断依赖于 `RoomEventData` 的配置
4. Redis键的命名规范需要保持一致
5. 日志记录有助于问题排查和性能监控
