package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 宠物活动喂养表
 *
 */
@TableName("t_pet_feed")
public class PetFeedData {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 宠物名字,为空表示没有取名字，取了名字不能再更改
     */
    private String petName;

    /**
     *  宠物类型 1 猫咪 2 猎豹 3 狮子
     */
    private Integer petType;

    /**
     *   1 正常领养 2 放养
     */
    private Integer petStatus;
    /**
     * 已喂养的食物总数量
     */
    private Integer eatTotalFood;

    /**
     * 宠物领养用户，uid为唯一键
     */
    private String uid;

    /**
     * 领养用户仓库食物总数量
     */
    private Integer food;

    /**
     * 领养用户仓库玩具总数量
     */
    private Integer toys;

    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer mtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPetName() {
        return petName;
    }

    public void setPetName(String petName) {
        this.petName = petName;
    }

    public Integer getPetType() {
        return petType;
    }

    public void setPetType(Integer petType) {
        this.petType = petType;
    }

    public Integer getPetStatus() {
        return petStatus;
    }

    public void setPetStatus(Integer petStatus) {
        this.petStatus = petStatus;
    }

    public Integer getEatTotalFood() {
        return eatTotalFood;
    }

    public void setEatTotalFood(Integer eatTotalFood) {
        this.eatTotalFood = eatTotalFood;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getFood() {
        return food;
    }

    public void setFood(Integer food) {
        this.food = food;
    }

    public Integer getToys() {
        return toys;
    }

    public void setToys(Integer toys) {
        this.toys = toys;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }
}
