package com.quhong.controllers;

import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.AnniversarySixService;
import com.quhong.service.PandaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 熊猫活动
 */
@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class AnniversaryController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(AnniversaryController.class);

    @Resource
    private AnniversarySixService anniversarySixService;

    /**
     * 六周年
     */
    @RequestMapping("memorySixthConfig")
    private String memoryConfig(@RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, anniversarySixService.memorySixthConfig(uid));
    }


    @RequestMapping("memorySixthShare")
    private String memorySixthShare(@RequestParam String uid, @RequestParam String picture, @RequestParam int slang,
                                    @RequestParam(required = false) String momentText) {
        anniversarySixService.memorySixthShare(uid, picture, slang, momentText);
        return createResult(HttpCode.SUCCESS, null);
    }


    @RequestMapping("anniversarySixthConfig")
    private String anniversarySixthConfig(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, anniversarySixService.anniversarySixthConfig(uid, activityId));
    }

    @RequestMapping("clickAnniversaryVideo")
    private String clickAnniversaryVideo(@RequestParam String uid, @RequestParam String activityId) {
        anniversarySixService.clickAnniversaryVideo(uid, activityId);
        return createResult(HttpCode.SUCCESS, null);
    }


}
