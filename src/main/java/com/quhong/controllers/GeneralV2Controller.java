package com.quhong.controllers;


import com.quhong.constant.ActivityHttpCode;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.H5Controller;
import com.quhong.service.AnniversaryV7Service;
import com.quhong.service.GiftCollectService;
import com.quhong.service.HorseRaceService;
import com.quhong.service.ShareService;
import com.quhong.service.WomenSupportService;
import com.quhong.service.*;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 杂七杂八的接口
 */


@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class GeneralV2Controller extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(GeneralV2Controller.class);

    @Resource
    private HorseRaceService horseRaceService;
    @Resource
    private GiftCollectService giftCollectService;
    @Resource
    private ShareService shareService;
    @Resource
    private WomenSupportService womenSupportService;
    @Resource
    private AnniversaryV7Service anniversaryV7Service;
    @Resource
    private CamelV2Service camelV2Service;
    @Resource
    private LuckyNumberService luckyNumberService;
    @Resource
    private CatchFishService catchFishService;
    @Resource
    private HalloweenService halloweenService;
    @Resource
    private PlantTreeV2Service plantTreeV2Service;
    @Resource
    private TalentHostService talentHostService;
    @Resource
    private OperationRoomWashService operationRoomWashService;
    @Resource
    private OperationRoomSelectService operationRoomSelectService;
    @Resource
    private KingOfJungleService kingOfJungleService;
    @Resource
    private TestService testService;
    @Resource
    private GloryOfCountriesService gloryOfCountriesService;
    @Resource
    private CarromMasterService carromMasterService;
    @Resource
    private LudoMasterService ludoMasterService;
    @Resource
    private GeneralService generalService;
    @Resource
    private MonsterCrushMasterService monsterCrushMasterService;
    @Resource
    private ScratchLotteryService scratchLotteryService;
    @Resource
    private UnoMasterService unoMasterService;
    @Resource
    private YourCircleGloryService yourCircleGloryService;
    @Resource
    private GiftIllustrationBookService giftIllustrationBookService;
    @Resource
    private OperationRoomCountrySelectService operationRoomCountrySelectService;
    @Resource
    private CatchFishService2025 catchFishService2025;
    @Resource
    private GameCarnivalService gameCarnivalService;

    @RequestMapping(value = "distributeDaily")
    private String distributeDaily(String dateStr) {
        logger.info("distributeDaily dateStr:{}", dateStr);
        plantTreeV2Service.dailyTaskRun(dateStr);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 赛马活动
     */
    @RequestMapping("horseRaceConfig")
    private String horseRaceConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, horseRaceService.horseRaceConfig(activityId, uid));
    }

    @RequestMapping(value = "horseRaceDraw")
    private String horseRaceDraw(@RequestParam String activityId, @RequestParam String uid, @RequestParam int zone, @RequestParam String cardType, @RequestParam int amount) {
        logger.info("horseRaceDraw activityId:{}, uid:{}, zone:{}, cardType:{}, amount:{}", activityId, uid, zone, cardType, amount);
        return createResult(HttpCode.SUCCESS, horseRaceService.horseRaceDraw(activityId, uid, zone, cardType, amount));
    }

    @RequestMapping("horseRaceRecord")
    private String horseRaceRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, horseRaceService.horseRaceDrawRecord(activityId, uid, page));
    }

    /**
     * 礼物收藏
     */
    @RequestMapping("giftCollectConfig")
    private String giftCollectConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, giftCollectService.giftCollectConfig(activityId, uid));
    }

    @RequestMapping("giftCollectCombine")
    private String giftCollectCombine(@RequestParam String activityId, @RequestParam String uid, @RequestParam String section, @RequestParam String resourceKey) {
        logger.info("giftCollectCombine: uid:{}, section:{}, resourceKey:{}", uid, section, resourceKey);
        giftCollectService.giftCollectCombine(activityId, uid, section, resourceKey);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping("giftCollectUpgrade")
    private String giftCollectUpgrade(@RequestParam String activityId, @RequestParam String uid, @RequestParam String section, @RequestParam int fromGiftId, @RequestParam int toGiftId) {
        logger.info("giftCollectUpgrade: uid:{}, section:{}, fromGiftId:{}, toGiftId:{}", uid, section, fromGiftId, toGiftId);
        giftCollectService.giftCollectUpgrade(activityId, uid, section, fromGiftId, toGiftId);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping("giftCollectRecord")
    private String giftCollectRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, giftCollectService.giftCollectRecord(activityId, uid, page));
    }

    /**
     * 房间活动分享列表
     */
    @RequestMapping("roomEventConfig")
    private String shareRoomEventConfig() {
        return createResult(HttpCode.SUCCESS, shareService.shareRoomEventConfig());
    }

    /**
     * 女性用户支持活动
     */
    @RequestMapping("womenSupport")
    private String womenSupport(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, womenSupportService.womenSupportConfig(activityId, uid));
    }

    @RequestMapping("womenSupportAddQueen")
    private String womenSupportAddQueen(@RequestParam String activityId, @RequestParam String uid, @RequestParam int queenType) {
        womenSupportService.womenSupportAddQueen(activityId, uid, queenType);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping("womenSupportPointRank")
    private String womenSupportPointRank(@RequestParam String activityId, @RequestParam String uid, @RequestParam int queenType) {
        return createResult(HttpCode.SUCCESS, womenSupportService.womenSupportPointRank(activityId, uid, queenType));
    }

    @RequestMapping("womenSupportHallRank")
    private String womenSupportHallRank(@RequestParam String activityId, @RequestParam String uid, @RequestParam int queenType, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, womenSupportService.womenSupportHallRank(activityId, uid, queenType, page));
    }

    /**
     * 周年打卡
     */
    @RequestMapping("anniversaryV1Config")
    private String anniversaryV1Config(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("anniversaryV1Config. activityId={} uid={}", activityId, uid);
        return createResult(HttpCode.SUCCESS, anniversaryV7Service.anniversaryV1Config(activityId, uid));
    }

    /**
     * 周年补签
     */
    @RequestMapping("anniversaryV1Sign")
    private String anniversaryV1Sign(@RequestParam String activityId, @RequestParam String uid, @RequestParam String signDate) {
        logger.info("anniversaryV1Sign. activityId={} uid={}, signDate={}", activityId, uid, signDate);
        anniversaryV7Service.anniversaryV1Sign(activityId, uid, signDate);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 周年参与周年庆
     */
    @RequestMapping("anniversaryV1Join")
    private String anniversaryV1Join(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("anniversaryV1Sign. activityId={} uid={}", activityId, uid);
        anniversaryV7Service.anniversaryV1Join(activityId, uid);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 周年设置日期
     */
    // @RequestMapping("anniversaryV1SetDate")
    // private String anniversaryV1SetDate(@RequestParam String activityId, @RequestParam String signDate) {
    //     logger.info("anniversaryV1Sign. activityId={} signDate={}", activityId, signDate);
    //     anniversaryV7Service.anniversaryV1SetDate(activityId, signDate);
    //     return createResult(HttpCode.SUCCESS, null);
    // }
    @RequestMapping("anniversaryV1ShareMoment")
    private String anniversaryV1ShareMoment(@RequestParam String uid, @RequestParam String activityId, @RequestParam int slang) {
        anniversaryV7Service.anniversaryV1ShareMoment(uid, activityId, slang);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 赛马活动
     */
    @RequestMapping("camelConfig")
    private String camelConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, camelV2Service.camelConfig(activityId, uid));
    }

    /**
     * 幸运数字活动
     */
    @RequestMapping("luckyNumberConfig")
    private String luckyNumberConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, luckyNumberService.luckyNumberConfig(activityId, uid));
    }

    @RequestMapping("luckyNumDraw")
    private String luckyNumDraw(@RequestParam String activityId, @RequestParam String uid, @RequestParam int gameType, @RequestParam int amount, @RequestParam(defaultValue = "1") String skin) {
        return createResult(HttpCode.SUCCESS, luckyNumberService.luckyNumDraw(activityId, uid, gameType, amount, skin));
    }

    @RequestMapping("luckyNumRecord")
    private String luckyNumRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int gameType, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, luckyNumberService.luckyNumRecord(activityId, uid, gameType, page));
    }

    // 赠送抽奖次数
    @RequestMapping("luckyNumSendTimes")
    private String luckyNumSendTimes(@RequestParam String activityId, @RequestParam String uid, @RequestParam String giveRid) {
        luckyNumberService.luckyNumSendTimes(activityId, uid, giveRid);
        return createResult(HttpCode.SUCCESS, null);
    }

    // 赠送记录
    @RequestMapping("luckyNumTimesRecord")
    private String luckyNumTimesRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, luckyNumberService.luckyNumTimesRecord(activityId, uid, page));
    }

    /**
     * 捕鱼
     */
    @RequestMapping("catchFishConfig")
    private String catchFishConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.PARAM_ERROR, null);
//        return createResult(HttpCode.SUCCESS, catchFishService.catchFishConfig(activityId, uid));
    }

    @RequestMapping("catchFishDraw")
    private String catchFishDraw(@RequestParam String activityId, @RequestParam String uid, @RequestParam String seaType, @RequestParam int amount, @RequestParam String metaId) {
        return createResult(HttpCode.SUCCESS, catchFishService.catchFishDraw(activityId, uid, seaType, amount, metaId));
    }

    @RequestMapping("catchFishRecord")
    private String catchFishRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, catchFishService.catchFishRecord(activityId, uid, page));
    }

    /**
     * 万圣节
     */
    @RequestMapping("halloweenConfig")
    private String halloweenConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, halloweenService.halloweenConfig(activityId, uid));
    }

    @RequestMapping("halloweenRecord")
    private String halloweenRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page, @RequestParam int type) {
        return createResult(HttpCode.SUCCESS, halloweenService.halloweenRecord(activityId, uid, page, type));
    }

    @RequestMapping("halloweenCatchSpirit")
    private String halloweenCatchSpirit(@RequestParam String activityId, @RequestParam String uid, @RequestParam String metaId) {
        return createResult(HttpCode.SUCCESS, halloweenService.halloweenCatchSpirit(activityId, uid, metaId));
    }

    @RequestMapping("halloweenExchange")
    private String halloweenExchange(@RequestParam String activityId, @RequestParam String uid, @RequestParam String metaId) {
        return createResult(HttpCode.SUCCESS, halloweenService.halloweenExchange(activityId, uid, metaId));
    }


    /**
     * 植树
     */
    @RequestMapping("plantTreeV2Config")
    private String plantTreeV2Config(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, plantTreeV2Service.plantTreeV2Config(activityId, uid));
    }

    @RequestMapping("plantTreeV2Watering")
    private String plantTreeV2Watering(@RequestParam String activityId, @RequestParam String uid, @RequestParam int num) {
        return createResult(HttpCode.SUCCESS, plantTreeV2Service.plantTreeV2Watering(activityId, uid, num));
    }

    // 邀请好友
    @RequestMapping("plantTreeV2InviteFriend")
    private String inviteFriend(@RequestParam String activityId, @RequestParam String uid, @RequestParam String aid, @RequestParam Integer position) {
        logger.info("inviteFriend. uid={} aid={}", uid, aid);
        if (!StringUtils.hasLength(aid) || position == null || position < 1 || position > 5) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return createResult(HttpCode.SUCCESS, plantTreeV2Service.inviteFriend(activityId, uid, aid, position));
    }

    // 移除已邀请的好友
    @RequestMapping("plantTreeV2RemoveInviteFriend")
    private String plantTreeV2RemoveInviteFriend(@RequestParam String activityId, @RequestParam String uid, @RequestParam String aid) {
        logger.info("removeInviteFriend. uid={} aid={}", uid, aid);
        if (!StringUtils.hasLength(aid)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return createResult(HttpCode.SUCCESS, plantTreeV2Service.removeInviteFriend(activityId, uid, aid));
    }

    @RequestMapping("plantTreeV2Sign")
    private String plantTreeV2Sign(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("plantTreeV2Sign. uid={} ", uid);
        plantTreeV2Service.plantTreeV2Sign(activityId, uid);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequestMapping("plantTreeV2SignReminder")
    private String plantTreeV2SignReminder(@RequestParam String activityId, @RequestParam String uid, @RequestParam(defaultValue = "1") int status) {
        logger.info("plantTreeV2SignReminder. uid={} ", uid);
        plantTreeV2Service.plantTreeV2SignReminder(activityId, uid, status);
        return createResult(HttpCode.SUCCESS, "");
    }

    /**
     * 才艺房主选拔赛
     */
    @RequestMapping("talentHostConfig")
    private String talentHostConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, talentHostService.talentHostConfig(activityId, uid));
    }

    @RequestMapping("talentHostNewUserList")
    private String talentHostNewUserList(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, talentHostService.talentHostNewUserList(activityId, uid, page));
    }

    @RequestMapping("talentHostSendMsg")
    private String talentHostSendMsg(@RequestParam String activityId, @RequestParam String uid, @RequestParam String aid) {
        talentHostService.talentHostSendMsg(activityId, uid, aid);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequestMapping("talentHostSelect")
    private String talentHostSelect(@RequestParam String activityId, @RequestParam String uid, @RequestParam int talentSelect) {
        talentHostService.talentHostSelect(activityId, uid, talentSelect);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequestMapping("talentPopularTaskReward")
    private String talentPopularTaskReward(@RequestParam String activityId, @RequestParam String uid, @RequestParam String taskKey) {
        talentHostService.talentPopularTaskReward(activityId, uid, taskKey);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequestMapping("talentHostTaskReward")
    private String talentHostTaskReward(@RequestParam String activityId, @RequestParam String uid, @RequestParam int taskType, @RequestParam String taskKey) {
        talentHostService.talentHostTaskReward(activityId, uid, taskType, taskKey);
        return createResult(HttpCode.SUCCESS, "");
    }

    /**
     * 运营房淘汰赛
     */
    @RequestMapping("operationRoomWashConfig")
    private String operationRoomWashConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, operationRoomWashService.operationRoomWashConfig(activityId, uid));
    }

    /**
     * 运营房选拔赛
     */
    @RequestMapping("operationRoomSelectConfig")
    private String operationRoomSelectConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, operationRoomSelectService.operationRoomSelectConfig(activityId, uid));
    }

    /**
     * 丛林之王活动
     */
    @RequestMapping("kingOfJungleInfo")
    private String kingOfJungleInfo(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, kingOfJungleService.kingOfJungleInfo(activityId, uid));
    }

    /**
     * 国家荣誉活动
     */
    @RequestMapping("gloryOfCountriesInfo")
    private String gloryOfCountriesInfo(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS,
                gloryOfCountriesService.gloryOfCountriesInfo(activityId, uid));
    }

    /**
     * 克罗姆大师 carromMasterInfo
     * type 0 克罗姆 1 ludo 2 monster 3 uno 100 game carnival
     */
    @RequestMapping("carromMasterInfo")
    private String carromMasterInfo(@RequestParam String activityId, @RequestParam String uid,
                                    @RequestParam(defaultValue = "0") int type) {
        CarromMasterVO vo = null;
        if (type == 0) {
            vo = carromMasterService.carromMasterInfo(activityId, uid);
        } else if (type == 1) {
            vo = ludoMasterService.ludoMasterInfo(activityId, uid);
        } else if (type == 2) {
            vo = monsterCrushMasterService.monsterMasterInfo(activityId, uid);
        } else if (type == 3) {
            vo = unoMasterService.unoMasterInfo(activityId, uid);
        }

        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 克罗姆大师推荐游戏 carromMasterRoomId
     * type 0 克罗姆 1 ludo 2 monster 3 uno
     */
    @RequestMapping("carromMasterRoomId")
    private String carromMasterRoomId(@RequestParam String activityId, @RequestParam String uid,
                                      @RequestParam(defaultValue = "0") int type) {
        CarromMasterVO vo = null;
        if (type == 0) {
            vo = carromMasterService.carromMasterRoomId(activityId, uid);
        } else if (type == 1) {
            vo = ludoMasterService.ludoMasterRoomId(activityId, uid);
        } else if (type == 2) {
            vo = monsterCrushMasterService.monsterMasterRoomId(activityId, uid);
        } else if (type == 3) {
            vo = unoMasterService.unoMasterRoomId(activityId, uid);
        }
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 克罗姆大师测试偏移天数 carromMasterTestUidDay
     * type 0 克罗姆 1 ludo 2 monster 3 uno
     */
    @RequestMapping("carromMasterTestUidDay")
    private String carromMasterTestUidDay(@RequestParam String activityId, @RequestParam String uid
            , @RequestParam int cmd, @RequestParam int addDays, @RequestParam int addValue
            , @RequestParam(defaultValue = "0") int type) {
        if (type == 0) {
            return createResult(HttpCode.SUCCESS, carromMasterService.testUidDay(cmd, uid, addDays, addValue));
        } else if (type == 1) {
            return createResult(HttpCode.SUCCESS, ludoMasterService.testUidDay(cmd, uid, addDays, addValue));
        } else if (type == 2) {
            return createResult(HttpCode.SUCCESS, monsterCrushMasterService.testUidDay(cmd, uid, addDays, addValue));
        } else if (type == 3) {
            return createResult(HttpCode.SUCCESS, unoMasterService.testUidDay(cmd, uid, addDays, addValue));
        } else if (type == 999) {
            return createResult(HttpCode.SUCCESS, unoMasterService.testUidDayBase(cmd, uid, addDays, activityId));
        } else {
            return createResult(HttpCode.PARAM_ERROR, "type error");
        }
    }

    /**
     * 水果互动礼物活动
     */
    @RequestMapping("fruitPartyConfig")
    private String fruitPartyConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, generalService.fruitPartyConfig(activityId, uid));
    }


    /**
     * 刮刮乐活动信息
     */
    @RequestMapping("/scratchLottery/info")
    private HttpResult<ScratchLotteryVO> scratchLotteryInfo(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("scratchLotteryInfo. uid={} activityId={}", uid, activityId);
        return HttpResult.getOk(scratchLotteryService.getInfo(activityId, uid));
    }

    /**
     * 刮刮乐抽奖
     */
    @RequestMapping("/scratchLottery/draw")
    private HttpResult<ScratchLotteryVO> scratchLotteryDraw(@RequestParam String uid, @RequestParam String activityId, @RequestParam int drawNum) {
        logger.info("scratchLotteryDraw. uid={} activityId={} drawNum={}", uid, activityId, drawNum);
        return HttpResult.getOk(scratchLotteryService.userDraw(activityId, uid, drawNum));
    }

    /**
     * 刮刮乐合成奖励
     */
    @RequestMapping("/scratchLottery/exchange")
    private HttpResult<ScratchLotteryVO> scratchLotteryExchange(@RequestParam String uid, @RequestParam String activityId, @RequestParam String taskKey) {
        logger.info("scratchLotteryExchange. uid={} activityId={} taskKey={}", uid, activityId, taskKey);
        return HttpResult.getOk(scratchLotteryService.exchange(activityId, uid, taskKey));
    }

    /**
     * 刮刮乐活动记录
     */
    @RequestMapping("/scratchLottery/record")
    private HttpResult<PageVO<ScratchLotteryRecordVO>> scratchLotteryRecord(@RequestParam String uid, @RequestParam String activityId, @RequestParam int type, @RequestParam(required = false, defaultValue = "1") int page) {
        logger.info("scratchLotteryRecord. uid={} activityId={} type={} page={}", uid, activityId, type, page);
        return HttpResult.getOk(scratchLotteryService.getRewardRecord(activityId, uid, type, page));
    }


    /**
     * 你的荣耀圈活动数据
     */
    @RequestMapping("/yourCircle/yourCircleConfig")
    private HttpResult<YourCircleVO> yourCircleConfig(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("scratchLotteryRecord. uid={} activityId={}", uid, activityId);
        return HttpResult.getOk(yourCircleGloryService.yourCircleConfig(activityId, uid));
    }


    /**
     * 礼物和平书基本信息
     */
    @RequestMapping("/giftPeaceBook/config")
    public HttpResult<GiftPeaceBook2025VO> giftPeaceBookConfig(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(giftIllustrationBookService.giftPeaceBookConfig(activityId, uid));
    }

    /**
     * 礼物和平书活动合成记录
     */
    @RequestMapping("/giftPeaceBook/history")
    public HttpResult<GiftPeaceBook2025VO.GiftPeaceBookHistoryVO> history(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return HttpResult.getOk(giftIllustrationBookService.getHistoryListPageRecord(activityId, uid, page));
    }


    /**
     * 礼物和平书活动合成
     */
    @RequestMapping("/giftPeaceBook/synthesis")
    public HttpResult<GiftPeaceBook2025VO> synthesis(@RequestParam String activityId, @RequestParam String uid, @RequestParam String collectType) {
        return HttpResult.getOk(giftIllustrationBookService.synthesis(activityId, uid, collectType));
    }

    /**
     * 运营房选拔赛
     */
    @RequestMapping("operationRoomCountrySelectConfig")
    private String operationRoomCountrySelectConfig(@RequestParam String activityId, @RequestParam String uid
            , @RequestParam(required = false, defaultValue = "") String countryCode) {
        return createResult(HttpCode.SUCCESS, operationRoomCountrySelectService.operationRoomSelectConfig(activityId, uid, countryCode));
    }


    /**
     * 运营房选拔赛-执行账号国家改IP国家转化
     */
    @RequestMapping("opExecuteCountryConversion")
    private String executeCountryConversion(@RequestParam String uid) {
        operationRoomCountrySelectService.executeCountryConversion();
        return createResult(HttpCode.SUCCESS, new Object());
    }

    /**
     * 运营房选拔赛-迁移单个用户国家
     */
    @RequestMapping("opExecuteSingleUserMigration")
    private String executeSingleUserMigration(@RequestParam String uid
            , String rid, String toIpCountry) {
        operationRoomCountrySelectService.executeSingleUserMigration(rid, toIpCountry);
        return createResult(HttpCode.SUCCESS, new Object());
    }

    /**
     * 捕鱼王活动2025
     */
    @RequestMapping("catchFishConfig2025")
    private HttpResult<CatchFishVO> catchFishConfig2025(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(catchFishService2025.catchFishConfig(activityId, uid));
    }

    @RequestMapping("catchFishDraw2025")
    private HttpResult<CatchFishVO> catchFishDraw2025(@RequestParam String activityId, @RequestParam String uid, @RequestParam String seaType, @RequestParam int amount, @RequestParam String metaId) {
        return HttpResult.getOk(catchFishService2025.catchFishDraw(activityId, uid, seaType, amount, metaId));
    }

    @RequestMapping("catchFishRecord2025")
    private HttpResult<CatchFishVO> catchFishRecord2025(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return HttpResult.getOk(catchFishService2025.catchFishRecord(activityId, uid, page));
    }

    @RequestMapping("catchFishRank2025")
    private HttpResult<CatchFishVO> catchFishRank2025(@RequestParam String activityId, @RequestParam String uid, @RequestParam int rankType) {
        return HttpResult.getOk(catchFishService2025.catchFishRank(activityId, uid, rankType));
    }

    @RequestMapping("gloryOfCountriesBadge")
    private HttpResult<GloryOfCountriesVO.BadgeRecordVO> badgeRecordInfo(@RequestParam String activityId, @RequestParam String uid, @RequestParam int badgeType) {
        return HttpResult.getOk(gloryOfCountriesService.badgeRecordInfo(activityId, uid, badgeType));
    }


    @RequestMapping("gameCarnivalInfo")
    private HttpResult<GameCarnivalVO> gameCarnivalInfo(@RequestParam String activityId, @RequestParam String uid) {
        GameCarnivalVO vo = gameCarnivalService.gameCarnivalInfo(activityId, uid);
        return HttpResult.getOk(vo);
    }

    /**
     *
     */
    @RequestMapping("gameRecommendRoomId")
    private HttpResult<GameCarnivalVO> gameRecommendRoomId(@RequestParam String activityId, @RequestParam String uid,
                                                           @RequestParam int gameType) {
        GameCarnivalVO vo = gameCarnivalService.recommendRoomId(activityId, uid, gameType);
        return HttpResult.getOk(vo);
    }

}
