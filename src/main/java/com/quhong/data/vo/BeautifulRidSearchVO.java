package com.quhong.data.vo;


import java.util.List;

public class BeautifulRidSearchVO {
    private int rid;
    private int status;
    private int can_take;
    private List<Integer> recommend;
    /**
     * 靓号等级颜色划分：
     * 红色(5)：1-2位数、字母+数字。例如：11、UAE、UAE12
     * 咖色(4)：3位数字。例如：333、456、321
     * 玫红(3)：4位数字。例如：3456、5435、67859
     * 蓝色(2)：5位数字。例如：34567、54355、67859
     * 绿色(1)：6位数字。例如：345356、456454、3458935
     * 0 非靓号
     */
    private int alphaLevel;

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getCan_take() {
        return can_take;
    }

    public void setCan_take(int can_take) {
        this.can_take = can_take;
    }

    public List<Integer> getRecommend() {
        return recommend;
    }

    public void setRecommend(List<Integer> recommend) {
        this.recommend = recommend;
    }

    public int getAlphaLevel() {
        return alphaLevel;
    }

    public void setAlphaLevel(int alphaLevel) {
        this.alphaLevel = alphaLevel;
    }
}
