package com.quhong.data.vo;


import com.quhong.mysql.data.UserFeedbackData;

import java.util.List;

public class UserFeedbackRecordVO {

    private List<UserFeedbackRecord> recordList;

    public static class UserFeedbackRecord extends UserFeedbackData{
        private List<String> images;

        public List<String> getImages() {
            return images;
        }

        public void setImages(List<String> images) {
            this.images = images;
        }
    }

    private int nextUrl;

    public List<UserFeedbackRecord> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<UserFeedbackRecord> recordList) {
        this.recordList = recordList;
    }

    public int getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(int nextUrl) {
        this.nextUrl = nextUrl;
    }
}
