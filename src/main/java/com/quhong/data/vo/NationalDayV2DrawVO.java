package com.quhong.data.vo;


public class NationalDayV2DrawVO {

    private Integer like; // 点赞人数
    private String awardIcon; // 奖励资源介绍图片
    private String rewardIndex;
    private String rewardType;
    private Integer sourceId; // 奖励资源id
    private String rewardIcon; // 奖励资源介绍图片
    private Integer rewardTime; //资源时长（天） 0永久
    private Integer rewardNum; // 礼物数量 可能为空
    private String rewardNameEn; // 奖励名称
    private String rewardNameAr; // 奖励名称阿语
    private String rewardRemarkEn; // 英语奖励备注(弹窗说明)
    private String rewardRemarkAr; // 阿语奖励备注(弹窗说明)

    // web配置和业务无关
    private Integer rewardTimes;

    public Integer getLike() {
        return like;
    }

    public void setLike(Integer like) {
        this.like = like;
    }

    public String getAwardIcon() {
        return awardIcon;
    }

    public void setAwardIcon(String awardIcon) {
        this.awardIcon = awardIcon;
    }

    public String getRewardIndex() {
        return rewardIndex;
    }

    public void setRewardIndex(String rewardIndex) {
        this.rewardIndex = rewardIndex;
    }

    public String getRewardType() {
        return rewardType;
    }

    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public String getRewardIcon() {
        return rewardIcon;
    }

    public void setRewardIcon(String rewardIcon) {
        this.rewardIcon = rewardIcon;
    }

    public Integer getRewardTime() {
        return rewardTime;
    }

    public void setRewardTime(Integer rewardTime) {
        this.rewardTime = rewardTime;
    }

    public Integer getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(Integer rewardNum) {
        this.rewardNum = rewardNum;
    }

    public String getRewardNameEn() {
        return rewardNameEn;
    }

    public void setRewardNameEn(String rewardNameEn) {
        this.rewardNameEn = rewardNameEn;
    }

    public String getRewardNameAr() {
        return rewardNameAr;
    }

    public void setRewardNameAr(String rewardNameAr) {
        this.rewardNameAr = rewardNameAr;
    }

    public String getRewardRemarkEn() {
        return rewardRemarkEn;
    }

    public void setRewardRemarkEn(String rewardRemarkEn) {
        this.rewardRemarkEn = rewardRemarkEn;
    }

    public String getRewardRemarkAr() {
        return rewardRemarkAr;
    }

    public void setRewardRemarkAr(String rewardRemarkAr) {
        this.rewardRemarkAr = rewardRemarkAr;
    }

    public Integer getRewardTimes() {
        return rewardTimes;
    }

    public void setRewardTimes(Integer rewardTimes) {
        this.rewardTimes = rewardTimes;
    }
}
