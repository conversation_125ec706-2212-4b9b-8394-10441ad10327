package com.quhong.data.vo;


import com.quhong.mysql.data.QuestionAwardData;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/5
 */
public class QuizActivityConfigVO {

    /**
     * id
     */
    private Integer id;

    /**
     * 活动名称
     */
    private String acName;

    /**
     * 活动名称阿语
     */
    private String acNameAr;

    /**
     * 活动开始时间
     */
    private Date acBeginTime;

    /**
     * 活动结束时间
     */
    private Date acEndTime;

    /**
     * 是否开始 0否 1是
     */
    private Integer status;

    /**
     * 首页背景
     */
    private String frontPageBgUrl;

    /**
     * 首页背景阿语
     */
    private String frontPageBgUrlAr;

    /**
     * 是否展示参与人数 0不展示 1展示
     */
    private Integer showJoinNum;

    /**
     * 参与人数
     */
    private Integer joinNum;

    /**
     * 答题次数 次/天
     */
    private Integer answersNum;

    /**
     * 参与方式 0免费次数
     */
    private Integer joinType;

    /**
     * 英文说明
     */
    private String explainEn;

    /**
     * 阿语说明
     */
    private String explainAr;

    /**
     * 活动规则
     */
    private String ruleEn;

    /**
     * 活动规则阿语
     */
    private String ruleAr;

    /**
     * 规则内容
     */
    private String ruleContent;

    /**
     * 规则内容阿语
     */
    private String ruleContentAr;

    /**
     * 开始作答按钮图片
     */
    private String beginBtnUrl;

    /**
     * 开始作答置灰按钮图片
     */
    private String beginGreyBtnUrl;

    /**
     * 活动说明按钮图片
     */
    private String frontPageExplainBtnUrl;

    /**
     * 活动说明按钮图片
     */
    private String explainBtnUrl;

    /**
     * 活动说明灰色按钮图片
     */
    private String explainGreyBtnUrl;

    /**
     * 首页排行榜按钮
     */
    private String frontPageRankBtnUrl;

    /**
     * 页面背景颜色
     */
    private String bgColor;

    /**
     * 返回按钮
     */
    private String backBtnUrl;

    /**
     * 说明页面卡片
     */
    private String explainPageUrl;

    /**
     * 说明页面卡片阿语
     */
    private String explainPageArUrl;

    /**
     * 排行榜页面卡片
     */
    private String rankPageUrl;

    /**
     * 排行榜页面卡片阿语
     */
    private String rankPageArUrl;

    /**
     * top1的css
     */
    private String topOneCss;

    /**
     * top2的css
     */
    private String topTwoCss;

    /**
     * top3的css
     */
    private String topThreeCss;

    /**
     * 答题页背景
     */
    private String answerBgUrl;

    /**
     * 答题页背景阿语
     */
    private String answerBgUrlAr;

    /**
     * 选项按钮
     */
    private String optionBtn;

    /**
     * 正确选项按钮
     */
    private String correctOptionBtn;

    /**
     * 错误选项按钮
     */
    private String errorOptionBtn;

    /**
     * 题目进度显示 0不显示 1显示
     */
    private Integer showRate;

    /**
     * 题库id
     */
    private String gid;

    /**
     * 阿语题库id
     */
    private String arGid;

    /**
     * 答题类型 0随机出题 1固定出题
     */
    private Integer answerType;

    /**
     * 单次出题数量
     */
    private Integer onceNum;

    /**
     * 每道题分数
     */
    private Integer score;

    /**
     * 计时方式 0顺序计时 1每题计时
     */
    private Integer timingMethod;

    /**
     * 每题时间
     */
    private Integer limitTime;

    /**
     * 是否显示正确答案 0不显示 1显示
     */
    private Integer showAnswer;

    /**
     * 结束页再次作答按钮图片url
     */
    private String againBtnUrl;

    /**
     * 结束页再次作答置灰按钮图片url
     */
    private String againGreyBtnUrl;

    /**
     * 结束页返回首页按钮图片url
     */
    private String backFrontPageBtnUrl;

    /**
     * 结束页排行榜按钮图片url
     */
    private String endPageRankBtnUrl;

    /**
     * 结束页卡片背景
     */
    private String endPageBgUrl;

    /**
     * 奖励背景图片
     */
    private String rewardBgUrl;

    /**
     * 正确卡片
     */
    private String correctCardUrl;

    /**
     * 错误卡片
     */
    private String errorCardUrl;

    /**
     * 按钮文本颜色
     */
    private String btnTextColor;

    /**
     * 说明/排行榜按钮文本颜色
     */
    private String otherBtnTextColor;

    /**
     * 规则文本颜色
     */
    private String explainTextColor;

    /**
     * 题目文本颜色
     */
    private String questionTextColor;

    /**
     * 卡片文本颜色
     */
    private String cardTextColor;

    private List<QuestionAwardData> reward;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAcName() {
        return acName;
    }

    public void setAcName(String acName) {
        this.acName = acName;
    }

    public String getAcNameAr() {
        return acNameAr;
    }

    public void setAcNameAr(String acNameAr) {
        this.acNameAr = acNameAr;
    }

    public Date getAcBeginTime() {
        return acBeginTime;
    }

    public void setAcBeginTime(Date acBeginTime) {
        this.acBeginTime = acBeginTime;
    }

    public Date getAcEndTime() {
        return acEndTime;
    }

    public void setAcEndTime(Date acEndTime) {
        this.acEndTime = acEndTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getFrontPageBgUrl() {
        return frontPageBgUrl;
    }

    public void setFrontPageBgUrl(String frontPageBgUrl) {
        this.frontPageBgUrl = frontPageBgUrl;
    }

    public String getFrontPageBgUrlAr() {
        return frontPageBgUrlAr;
    }

    public void setFrontPageBgUrlAr(String frontPageBgUrlAr) {
        this.frontPageBgUrlAr = frontPageBgUrlAr;
    }

    public Integer getShowJoinNum() {
        return showJoinNum;
    }

    public void setShowJoinNum(Integer showJoinNum) {
        this.showJoinNum = showJoinNum;
    }

    public Integer getJoinNum() {
        return joinNum;
    }

    public void setJoinNum(Integer joinNum) {
        this.joinNum = joinNum;
    }

    public Integer getAnswersNum() {
        return answersNum;
    }

    public void setAnswersNum(Integer answersNum) {
        this.answersNum = answersNum;
    }

    public Integer getJoinType() {
        return joinType;
    }

    public void setJoinType(Integer joinType) {
        this.joinType = joinType;
    }

    public String getExplainEn() {
        return explainEn;
    }

    public void setExplainEn(String explainEn) {
        this.explainEn = explainEn;
    }

    public String getExplainAr() {
        return explainAr;
    }

    public void setExplainAr(String explainAr) {
        this.explainAr = explainAr;
    }

    public String getRuleEn() {
        return ruleEn;
    }

    public void setRuleEn(String ruleEn) {
        this.ruleEn = ruleEn;
    }

    public String getRuleAr() {
        return ruleAr;
    }

    public void setRuleAr(String ruleAr) {
        this.ruleAr = ruleAr;
    }

    public String getRuleContent() {
        return ruleContent;
    }

    public void setRuleContent(String ruleContent) {
        this.ruleContent = ruleContent;
    }

    public String getRuleContentAr() {
        return ruleContentAr;
    }

    public void setRuleContentAr(String ruleContentAr) {
        this.ruleContentAr = ruleContentAr;
    }

    public String getBeginBtnUrl() {
        return beginBtnUrl;
    }

    public void setBeginBtnUrl(String beginBtnUrl) {
        this.beginBtnUrl = beginBtnUrl;
    }

    public String getBeginGreyBtnUrl() {
        return beginGreyBtnUrl;
    }

    public void setBeginGreyBtnUrl(String beginGreyBtnUrl) {
        this.beginGreyBtnUrl = beginGreyBtnUrl;
    }

    public String getExplainBtnUrl() {
        return explainBtnUrl;
    }

    public void setExplainBtnUrl(String explainBtnUrl) {
        this.explainBtnUrl = explainBtnUrl;
    }

    public String getBgColor() {
        return bgColor;
    }

    public void setBgColor(String bgColor) {
        this.bgColor = bgColor;
    }

    public String getBackBtnUrl() {
        return backBtnUrl;
    }

    public void setBackBtnUrl(String backBtnUrl) {
        this.backBtnUrl = backBtnUrl;
    }

    public String getExplainPageUrl() {
        return explainPageUrl;
    }

    public void setExplainPageUrl(String explainPageUrl) {
        this.explainPageUrl = explainPageUrl;
    }

    public String getExplainPageArUrl() {
        return explainPageArUrl;
    }

    public void setExplainPageArUrl(String explainPageArUrl) {
        this.explainPageArUrl = explainPageArUrl;
    }

    public String getRankPageUrl() {
        return rankPageUrl;
    }

    public void setRankPageUrl(String rankPageUrl) {
        this.rankPageUrl = rankPageUrl;
    }

    public String getRankPageArUrl() {
        return rankPageArUrl;
    }

    public void setRankPageArUrl(String rankPageArUrl) {
        this.rankPageArUrl = rankPageArUrl;
    }

    public String getTopOneCss() {
        return topOneCss;
    }

    public void setTopOneCss(String topOneCss) {
        this.topOneCss = topOneCss;
    }

    public String getTopTwoCss() {
        return topTwoCss;
    }

    public void setTopTwoCss(String topTwoCss) {
        this.topTwoCss = topTwoCss;
    }

    public String getTopThreeCss() {
        return topThreeCss;
    }

    public void setTopThreeCss(String topThreeCss) {
        this.topThreeCss = topThreeCss;
    }

    public String getAnswerBgUrl() {
        return answerBgUrl;
    }

    public void setAnswerBgUrl(String answerBgUrl) {
        this.answerBgUrl = answerBgUrl;
    }

    public String getAnswerBgUrlAr() {
        return answerBgUrlAr;
    }

    public void setAnswerBgUrlAr(String answerBgUrlAr) {
        this.answerBgUrlAr = answerBgUrlAr;
    }

    public String getOptionBtn() {
        return optionBtn;
    }

    public void setOptionBtn(String optionBtn) {
        this.optionBtn = optionBtn;
    }

    public Integer getShowRate() {
        return showRate;
    }

    public void setShowRate(Integer showRate) {
        this.showRate = showRate;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getArGid() {
        return arGid;
    }

    public void setArGid(String arGid) {
        this.arGid = arGid;
    }

    public Integer getAnswerType() {
        return answerType;
    }

    public void setAnswerType(Integer answerType) {
        this.answerType = answerType;
    }

    public Integer getOnceNum() {
        return onceNum;
    }

    public void setOnceNum(Integer onceNum) {
        this.onceNum = onceNum;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Integer getTimingMethod() {
        return timingMethod;
    }

    public void setTimingMethod(Integer timingMethod) {
        this.timingMethod = timingMethod;
    }

    public Integer getLimitTime() {
        return limitTime;
    }

    public void setLimitTime(Integer limitTime) {
        this.limitTime = limitTime;
    }

    public Integer getShowAnswer() {
        return showAnswer;
    }

    public void setShowAnswer(Integer showAnswer) {
        this.showAnswer = showAnswer;
    }

    public String getAgainBtnUrl() {
        return againBtnUrl;
    }

    public void setAgainBtnUrl(String againBtnUrl) {
        this.againBtnUrl = againBtnUrl;
    }

    public String getAgainGreyBtnUrl() {
        return againGreyBtnUrl;
    }

    public void setAgainGreyBtnUrl(String againGreyBtnUrl) {
        this.againGreyBtnUrl = againGreyBtnUrl;
    }

    public String getBackFrontPageBtnUrl() {
        return backFrontPageBtnUrl;
    }

    public void setBackFrontPageBtnUrl(String backFrontPageBtnUrl) {
        this.backFrontPageBtnUrl = backFrontPageBtnUrl;
    }

    public String getEndPageRankBtnUrl() {
        return endPageRankBtnUrl;
    }

    public void setEndPageRankBtnUrl(String endPageRankBtnUrl) {
        this.endPageRankBtnUrl = endPageRankBtnUrl;
    }

    public String getEndPageBgUrl() {
        return endPageBgUrl;
    }

    public void setEndPageBgUrl(String endPageBgUrl) {
        this.endPageBgUrl = endPageBgUrl;
    }

    public String getCorrectCardUrl() {
        return correctCardUrl;
    }

    public void setCorrectCardUrl(String correctCardUrl) {
        this.correctCardUrl = correctCardUrl;
    }

    public String getErrorCardUrl() {
        return errorCardUrl;
    }

    public void setErrorCardUrl(String errorCardUrl) {
        this.errorCardUrl = errorCardUrl;
    }

    public String getBtnTextColor() {
        return btnTextColor;
    }

    public void setBtnTextColor(String btnTextColor) {
        this.btnTextColor = btnTextColor;
    }

    public String getOtherBtnTextColor() {
        return otherBtnTextColor;
    }

    public void setOtherBtnTextColor(String otherBtnTextColor) {
        this.otherBtnTextColor = otherBtnTextColor;
    }

    public String getExplainTextColor() {
        return explainTextColor;
    }

    public void setExplainTextColor(String explainTextColor) {
        this.explainTextColor = explainTextColor;
    }

    public String getQuestionTextColor() {
        return questionTextColor;
    }

    public void setQuestionTextColor(String questionTextColor) {
        this.questionTextColor = questionTextColor;
    }

    public String getCardTextColor() {
        return cardTextColor;
    }

    public void setCardTextColor(String cardTextColor) {
        this.cardTextColor = cardTextColor;
    }

    public String getFrontPageExplainBtnUrl() {
        return frontPageExplainBtnUrl;
    }

    public void setFrontPageExplainBtnUrl(String frontPageExplainBtnUrl) {
        this.frontPageExplainBtnUrl = frontPageExplainBtnUrl;
    }

    public List<QuestionAwardData> getReward() {
        return reward;
    }

    public void setReward(List<QuestionAwardData> reward) {
        this.reward = reward;
    }

    public String getExplainGreyBtnUrl() {
        return explainGreyBtnUrl;
    }

    public void setExplainGreyBtnUrl(String explainGreyBtnUrl) {
        this.explainGreyBtnUrl = explainGreyBtnUrl;
    }

    public String getFrontPageRankBtnUrl() {
        return frontPageRankBtnUrl;
    }

    public void setFrontPageRankBtnUrl(String frontPageRankBtnUrl) {
        this.frontPageRankBtnUrl = frontPageRankBtnUrl;
    }

    public String getCorrectOptionBtn() {
        return correctOptionBtn;
    }

    public void setCorrectOptionBtn(String correctOptionBtn) {
        this.correctOptionBtn = correctOptionBtn;
    }

    public String getErrorOptionBtn() {
        return errorOptionBtn;
    }

    public void setErrorOptionBtn(String errorOptionBtn) {
        this.errorOptionBtn = errorOptionBtn;
    }

    public String getRewardBgUrl() {
        return rewardBgUrl;
    }

    public void setRewardBgUrl(String rewardBgUrl) {
        this.rewardBgUrl = rewardBgUrl;
    }
}
