package com.quhong.data.vo;


import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class GiftPeaceBook2025VO extends OtherRankConfigVO {
    private List<Integer> giftRemainNumList; // 各礼物卡片剩余数
    private Integer allGiftLightNum; // 所有已经点亮的礼物种类数
    private List<OtherSupportUserVO>  giftSupportOneUserList; // 礼物top1支持者列表
    private List<Integer> synthesisNumList; // 6个合成类型，当前可用的子卡片数量,没项取值范围为0-3

    private SynthesisVO synthesisVO; // 合成后返回的对象

    public static class ResourceMetaTmp extends ResourceKeyConfigData.ResourceMeta {
        private List<String> giftIconList; //
        private int ctime; // 历史记录用，抽奖时间

        public List<String> getGiftIconList() {
            return giftIconList;
        }

        public void setGiftIconList(List<String> giftIconList) {
            this.giftIconList = giftIconList;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }
    }

    public static class GiftPeaceBookHistoryVO {
        private int nextPage;
        private List<ResourceMetaTmp> myHistoryList;

        public int getNextPage() {
            return nextPage;
        }

        public void setNextPage(int nextPage) {
            this.nextPage = nextPage;
        }

        public List<ResourceMetaTmp> getMyHistoryList() {
            return myHistoryList;
        }

        public void setMyHistoryList(List<ResourceMetaTmp> myHistoryList) {
            this.myHistoryList = myHistoryList;
        }
    }

    public static class SynthesisVO {
        ResourceKeyConfigData.ResourceMeta resourceMeta;

        public ResourceKeyConfigData.ResourceMeta getResourceMeta() {
            return resourceMeta;
        }

        public void setResourceMeta(ResourceKeyConfigData.ResourceMeta resourceMeta) {
            this.resourceMeta = resourceMeta;
        }
    }

    public List<Integer> getGiftRemainNumList() {
        return giftRemainNumList;
    }

    public void setGiftRemainNumList(List<Integer> giftRemainNumList) {
        this.giftRemainNumList = giftRemainNumList;
    }

    public SynthesisVO getSynthesisVO() {
        return synthesisVO;
    }

    public void setSynthesisVO(SynthesisVO synthesisVO) {
        this.synthesisVO = synthesisVO;
    }

    public Integer getAllGiftLightNum() {
        return allGiftLightNum;
    }

    public void setAllGiftLightNum(Integer allGiftLightNum) {
        this.allGiftLightNum = allGiftLightNum;
    }

    public List<OtherSupportUserVO> getGiftSupportOneUserList() {
        return giftSupportOneUserList;
    }

    public void setGiftSupportOneUserList(List<OtherSupportUserVO> giftSupportOneUserList) {
        this.giftSupportOneUserList = giftSupportOneUserList;
    }

    public List<Integer> getSynthesisNumList() {
        return synthesisNumList;
    }

    public void setSynthesisNumList(List<Integer> synthesisNumList) {
        this.synthesisNumList = synthesisNumList;
    }
}
