package com.quhong.data.vo;


import java.util.List;
import java.util.Set;

public class LlluminateYouStarVO extends OtherRankConfigVO {

    private Integer joinState;   // 0 未加入 1 已加入
    private Integer firstOpen; // 是否第一次打开闪耀tab  1 是 0不是
    private List<DetailVO> detailVOList; // 每日详情 未加入返回空
    private String coverUrl;   // 活动封面
    private String hostHead;
    private String hostName;
    private String mangerHead;
    private String mangerName;
    private Integer unRead; // 未读官方消息数

    private List<RankVO> totalRankList; // 总榜
    private List<RankVO> dayRankList; // 日榜

    private RankVO myDayRank; // 我的日榜
    private RankVO myTotalRank; // 我的总榜

    private List<RoomItemVO> dailyShiningList; // 左tab-每日闪耀房间
    private List<RoomItemVO> hotEventList; // 右tab-热门房间活动

    // 抽奖部分
    private Integer currentPoints; // 当前实际积分
    private Integer levelPoints;  // 当前等级积分
    private DetailVO myDetailVO;
    private List<ResourceDataVO> rollList;
    private List<ResourceDataVO> myHistoryList;
    private Integer leftDrawTimes; // 剩余抽奖次数
    private Integer firstDrawReward; // 是否有首抽免费奖励 1 是 0 不是

    private List<ResourceDataVO> resultList;
    private Integer firstDraw; // 是否为未参加活动设备第一次抽奖 1 是 0 不是

    public static class DetailVO {
        private String dayStr;   // 当前日期 "yyyy-MM-dd"
        private Integer dayPoint; // 当日积分
        private Long sendGift; // 发送总钻石数
        private Integer oldUserMic; // 老用户上麦人数
        private Integer newUserMic; // 新用户上麦人数
        private Integer oldUserSubNum; // 老用户订阅数
        private Integer newUserSubNum; // 新用户订阅数
        private Integer totalUserSubNum; // 总用户订阅数，展示字段
        private Integer totalUserMic; // 总上麦人数，展示字段
        private Integer rank;// 10名之后为-1，展示字段
        private Set<String> micUsers; // 当日上麦用户排重，统计字段
        private Set<String> subUsers; // 当日订阅用户排重，统计字段

        public String getDayStr() {
            return dayStr;
        }

        public void setDayStr(String dayStr) {
            this.dayStr = dayStr;
        }

        public Integer getDayPoint() {
            return dayPoint;
        }

        public void setDayPoint(Integer dayPoint) {
            this.dayPoint = dayPoint;
        }

        public Long getSendGift() {
            return sendGift;
        }

        public void setSendGift(Long sendGift) {
            this.sendGift = sendGift;
        }

        public Integer getOldUserMic() {
            return oldUserMic;
        }

        public void setOldUserMic(Integer oldUserMic) {
            this.oldUserMic = oldUserMic;
        }

        public Integer getNewUserMic() {
            return newUserMic;
        }

        public void setNewUserMic(Integer newUserMic) {
            this.newUserMic = newUserMic;
        }

        public int getOldUserSubNum() {
            return oldUserSubNum == null ? 0 : oldUserSubNum;
        }

        public void setOldUserSubNum(Integer oldUserSubNum) {
            this.oldUserSubNum = oldUserSubNum;
        }

        public int getNewUserSubNum() {
            return newUserSubNum == null ? 0 : newUserSubNum;
        }

        public void setNewUserSubNum(Integer newUserSubNum) {
            this.newUserSubNum = newUserSubNum;
        }

        public Integer getTotalUserSubNum() {
            return totalUserSubNum;
        }

        public void setTotalUserSubNum(Integer totalUserSubNum) {
            this.totalUserSubNum = totalUserSubNum;
        }

        public Integer getTotalUserMic() {
            return totalUserMic;
        }

        public void setTotalUserMic(Integer totalUserMic) {
            this.totalUserMic = totalUserMic;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public Set<String> getMicUsers() {
            return micUsers;
        }

        public void setMicUsers(Set<String> micUsers) {
            this.micUsers = micUsers;
        }

        public Set<String> getSubUsers() {
            return subUsers;
        }

        public void setSubUsers(Set<String> subUsers) {
            this.subUsers = subUsers;
        }
    }

    public static class RankVO {
        private String hostHead;
        private String hostName;
        private String mangerHead;
        private String mangerName;
        private String countryFlag;
        private Integer rank;
        private Integer points;
        private String roomId;

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public String getHostHead() {
            return hostHead;
        }

        public void setHostHead(String hostHead) {
            this.hostHead = hostHead;
        }

        public String getHostName() {
            return hostName;
        }

        public void setHostName(String hostName) {
            this.hostName = hostName;
        }

        public String getMangerHead() {
            return mangerHead;
        }

        public void setMangerHead(String mangerHead) {
            this.mangerHead = mangerHead;
        }

        public String getMangerName() {
            return mangerName;
        }

        public void setMangerName(String mangerName) {
            this.mangerName = mangerName;
        }

        public String getCountryFlag() {
            return countryFlag;
        }

        public void setCountryFlag(String countryFlag) {
            this.countryFlag = countryFlag;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public Integer getPoints() {
            return points;
        }

        public void setPoints(Integer points) {
            this.points = points;
        }
    }

    public static class RoomItemVO {
        private String roomId;
        // 每日闪耀左tab
        private String roomCover; //房间封面
        private Integer roomOnline; // 房间人数
        private String countryFlag;
        private String roomName;
        private Integer points; // 房间积分
        private String hostHead;
        private String mangerHead;
        private Integer roomLevel;
        private String roomTypeIcon;
        private String roomTypeName;
        private String roomLevelIcon;

        // 每日闪耀右tab
        private String roomEventCover; //房间活动封面
        private Integer roomEventState; // 房间活动状态 0待开始 1进行中
        private Integer roomEventStartTime; // 活动开始时间
        private String roomEventName; // 活动名称
        private Integer roomEventSubCount; // 订阅人数
        private Integer roomEventSubState; // 房间活动订阅状态 0未订阅 1已订阅
        private Integer roomEventId; // 房间活动id

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public String getRoomTypeIcon() {
            return roomTypeIcon;
        }

        public void setRoomTypeIcon(String roomTypeIcon) {
            this.roomTypeIcon = roomTypeIcon;
        }

        public String getRoomTypeName() {
            return roomTypeName;
        }

        public void setRoomTypeName(String roomTypeName) {
            this.roomTypeName = roomTypeName;
        }

        public String getRoomCover() {
            return roomCover;
        }

        public void setRoomCover(String roomCover) {
            this.roomCover = roomCover;
        }

        public Integer getRoomOnline() {
            return roomOnline;
        }

        public void setRoomOnline(Integer roomOnline) {
            this.roomOnline = roomOnline;
        }

        public String getCountryFlag() {
            return countryFlag;
        }

        public void setCountryFlag(String countryFlag) {
            this.countryFlag = countryFlag;
        }

        public String getRoomName() {
            return roomName;
        }

        public void setRoomName(String roomName) {
            this.roomName = roomName;
        }

        public Integer getPoints() {
            return points;
        }

        public void setPoints(Integer points) {
            this.points = points;
        }

        public String getHostHead() {
            return hostHead;
        }

        public void setHostHead(String hostHead) {
            this.hostHead = hostHead;
        }

        public String getMangerHead() {
            return mangerHead;
        }

        public void setMangerHead(String mangerHead) {
            this.mangerHead = mangerHead;
        }

        public Integer getRoomLevel() {
            return roomLevel;
        }

        public void setRoomLevel(Integer roomLevel) {
            this.roomLevel = roomLevel;
        }

        public String getRoomEventCover() {
            return roomEventCover;
        }

        public void setRoomEventCover(String roomEventCover) {
            this.roomEventCover = roomEventCover;
        }

        public Integer getRoomEventState() {
            return roomEventState;
        }

        public void setRoomEventState(Integer roomEventState) {
            this.roomEventState = roomEventState;
        }

        public Integer getRoomEventStartTime() {
            return roomEventStartTime;
        }

        public void setRoomEventStartTime(Integer roomEventStartTime) {
            this.roomEventStartTime = roomEventStartTime;
        }

        public String getRoomEventName() {
            return roomEventName;
        }

        public void setRoomEventName(String roomEventName) {
            this.roomEventName = roomEventName;
        }

        public Integer getRoomEventSubCount() {
            return roomEventSubCount;
        }

        public void setRoomEventSubCount(Integer roomEventSubCount) {
            this.roomEventSubCount = roomEventSubCount;
        }

        public String getRoomLevelIcon() {
            return roomLevelIcon;
        }

        public void setRoomLevelIcon(String roomLevelIcon) {
            this.roomLevelIcon = roomLevelIcon;
        }

        public Integer getRoomEventSubState() {
            return roomEventSubState;
        }

        public void setRoomEventSubState(Integer roomEventSubState) {
            this.roomEventSubState = roomEventSubState;
        }

        public Integer getRoomEventId() {
            return roomEventId;
        }

        public void setRoomEventId(Integer roomEventId) {
            this.roomEventId = roomEventId;
        }
    }

    public static class ResourceDataVO {
        private String name;  //  滚屏的名字，只有滚屏数据返回有

        private String metaId;               // 奖品id
        private String resourceIcon;         // 奖品图标
        private String resourcePreview;      // 奖品预览图
        private String resourceNameEn;       // 奖品名称英语
        private String resourceNameAr;       // 奖品名称阿语
        private Integer resourcePrice;           // 奖品价值
        private Integer resourceType;            // 奖品奖励类型
        private Integer resourceId;              // 奖品资源id
        private Integer resourceTime;            // 奖品奖励时长
        private Integer resourceNumber;          // 奖品奖励数量

        public String getMetaId() {
            return metaId;
        }

        public void setMetaId(String metaId) {
            this.metaId = metaId;
        }

        public String getResourceIcon() {
            return resourceIcon;
        }

        public void setResourceIcon(String resourceIcon) {
            this.resourceIcon = resourceIcon;
        }

        public String getResourcePreview() {
            return resourcePreview;
        }

        public void setResourcePreview(String resourcePreview) {
            this.resourcePreview = resourcePreview;
        }

        public String getResourceNameEn() {
            return resourceNameEn;
        }

        public void setResourceNameEn(String resourceNameEn) {
            this.resourceNameEn = resourceNameEn;
        }

        public String getResourceNameAr() {
            return resourceNameAr;
        }

        public void setResourceNameAr(String resourceNameAr) {
            this.resourceNameAr = resourceNameAr;
        }

        public Integer getResourcePrice() {
            return resourcePrice;
        }

        public void setResourcePrice(Integer resourcePrice) {
            this.resourcePrice = resourcePrice;
        }

        public Integer getResourceType() {
            return resourceType;
        }

        public void setResourceType(Integer resourceType) {
            this.resourceType = resourceType;
        }

        public Integer getResourceId() {
            return resourceId;
        }

        public void setResourceId(Integer resourceId) {
            this.resourceId = resourceId;
        }

        public Integer getResourceTime() {
            return resourceTime;
        }

        public void setResourceTime(Integer resourceTime) {
            this.resourceTime = resourceTime;
        }

        public Integer getResourceNumber() {
            return resourceNumber;
        }

        public void setResourceNumber(Integer resourceNumber) {
            this.resourceNumber = resourceNumber;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public Integer getJoinState() {
        return joinState;
    }

    public void setJoinState(Integer joinState) {
        this.joinState = joinState;
    }


    public Integer getFirstOpen() {
        return firstOpen;
    }

    public void setFirstOpen(Integer firstOpen) {
        this.firstOpen = firstOpen;
    }

    public List<DetailVO> getDetailVOList() {
        return detailVOList;
    }

    public void setDetailVOList(List<DetailVO> detailVOList) {
        this.detailVOList = detailVOList;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getHostHead() {
        return hostHead;
    }

    public void setHostHead(String hostHead) {
        this.hostHead = hostHead;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public String getMangerHead() {
        return mangerHead;
    }

    public void setMangerHead(String mangerHead) {
        this.mangerHead = mangerHead;
    }

    public String getMangerName() {
        return mangerName;
    }

    public void setMangerName(String mangerName) {
        this.mangerName = mangerName;
    }

    public List<RankVO> getTotalRankList() {
        return totalRankList;
    }

    public void setTotalRankList(List<RankVO> totalRankList) {
        this.totalRankList = totalRankList;
    }

    public List<RankVO> getDayRankList() {
        return dayRankList;
    }

    public void setDayRankList(List<RankVO> dayRankList) {
        this.dayRankList = dayRankList;
    }

    public RankVO getMyDayRank() {
        return myDayRank;
    }

    public void setMyDayRank(RankVO myDayRank) {
        this.myDayRank = myDayRank;
    }

    public RankVO getMyTotalRank() {
        return myTotalRank;
    }

    public void setMyTotalRank(RankVO myTotalRank) {
        this.myTotalRank = myTotalRank;
    }

    public List<RoomItemVO> getDailyShiningList() {
        return dailyShiningList;
    }

    public void setDailyShiningList(List<RoomItemVO> dailyShiningList) {
        this.dailyShiningList = dailyShiningList;
    }

    public List<RoomItemVO> getHotEventList() {
        return hotEventList;
    }

    public void setHotEventList(List<RoomItemVO> hotEventList) {
        this.hotEventList = hotEventList;
    }

    public Integer getCurrentPoints() {
        return currentPoints;
    }

    public void setCurrentPoints(Integer currentPoints) {
        this.currentPoints = currentPoints;
    }

    public Integer getLevelPoints() {
        return levelPoints;
    }

    public void setLevelPoints(Integer levelPoints) {
        this.levelPoints = levelPoints;
    }

    public List<ResourceDataVO> getRollList() {
        return rollList;
    }

    public void setRollList(List<ResourceDataVO> rollList) {
        this.rollList = rollList;
    }

    public List<ResourceDataVO> getMyHistoryList() {
        return myHistoryList;
    }

    public void setMyHistoryList(List<ResourceDataVO> myHistoryList) {
        this.myHistoryList = myHistoryList;
    }

    public List<ResourceDataVO> getResultList() {
        return resultList;
    }

    public void setResultList(List<ResourceDataVO> resultList) {
        this.resultList = resultList;
    }

    public DetailVO getMyDetailVO() {
        return myDetailVO;
    }

    public void setMyDetailVO(DetailVO myDetailVO) {
        this.myDetailVO = myDetailVO;
    }

    public Integer getFirstDraw() {
        return firstDraw;
    }

    public void setFirstDraw(Integer firstDraw) {
        this.firstDraw = firstDraw;
    }

    public Integer getLeftDrawTimes() {
        return leftDrawTimes;
    }

    public void setLeftDrawTimes(Integer leftDrawTimes) {
        this.leftDrawTimes = leftDrawTimes;
    }

    public Integer getUnRead() {
        return unRead;
    }

    public void setUnRead(Integer unRead) {
        this.unRead = unRead;
    }

    public Integer getFirstDrawReward() {
        return firstDrawReward;
    }

    public void setFirstDrawReward(Integer firstDrawReward) {
        this.firstDrawReward = firstDrawReward;
    }
}
