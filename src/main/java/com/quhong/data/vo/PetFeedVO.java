package com.quhong.data.vo;


import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class PetFeedVO extends OtherRankConfigVO {
    private List<TaskConfigVO> dailyTaskList; // 每日基础任务
    private List<TaskConfigVO> dailyAdvanceTaskList; // 每日进阶任务

    public static class MyInfo {
        private int food; // 食物数量
        private int toys; // 玩具数量
        private int love; // 爱心数量
        private String signDate; // 最后一次的签到日期 yyyy-MM-dd
        private int signDay; // 签到天数
        private int signReminderStatus; // 签到提醒 0已关闭 1已开启
        private int isDoneMyPetFood; // 是否完成对自己宠物喂养食物任务 0未完成 1完成

        public int getFood() {
            return food;
        }

        public void setFood(int food) {
            this.food = food;
        }

        public int getToys() {
            return toys;
        }

        public void setToys(int toys) {
            this.toys = toys;
        }

        public int getLove() {
            return love;
        }

        public void setLove(int love) {
            this.love = love;
        }

        public String getSignDate() {
            return signDate;
        }

        public void setSignDate(String signDate) {
            this.signDate = signDate;
        }

        public int getSignDay() {
            return signDay;
        }

        public void setSignDay(int signDay) {
            this.signDay = signDay;
        }

        public int getSignReminderStatus() {
            return signReminderStatus;
        }

        public void setSignReminderStatus(int signReminderStatus) {
            this.signReminderStatus = signReminderStatus;
        }

        public int getIsDoneMyPetFood() {
            return isDoneMyPetFood;
        }

        public void setIsDoneMyPetFood(int isDoneMyPetFood) {
            this.isDoneMyPetFood = isDoneMyPetFood;
        }
    }

    public static class PetInfo {
        private String petName; // 宠物名字
        private int petType; // 宠物类型 1 猫咪 2 猎豹 3 狮子
        private int ctime; // 宠物领取时间
        private int eatFood; // 当日已喂养的食物数量
        private int playToys; // 当日已玩耍的玩具数量
        private int eatTotalFood; // 已喂养的食物总数量

        public String getPetName() {
            return petName;
        }

        public void setPetName(String petName) {
            this.petName = petName;
        }

        public int getPetType() {
            return petType;
        }

        public void setPetType(int petType) {
            this.petType = petType;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }

        public int getEatFood() {
            return eatFood;
        }

        public void setEatFood(int eatFood) {
            this.eatFood = eatFood;
        }

        public int getPlayToys() {
            return playToys;
        }

        public void setPlayToys(int playToys) {
            this.playToys = playToys;
        }

        public int getEatTotalFood() {
            return eatTotalFood;
        }

        public void setEatTotalFood(int eatTotalFood) {
            this.eatTotalFood = eatTotalFood;
        }
    }

    public static class DailyTaskInfo {
        // 每日基础任务
        private int shareSnapchatStatus; // 分享到snapchat状态
        private int onMic5Status; // 完成上麦5分钟状态
        private int sendGiftDiamond; // 送礼钻石数
        private int receiveGiftDiamond; // 收礼钻石数
        // 每日进阶任务
        private int rechargeTimes; // 充值110钻以上的笔数，包含所有渠道
        private int inviteNewUser; // 邀请新用户数量
        private int inviteOldUser; // 召回老朋友数量

        public int getShareSnapchatStatus() {
            return shareSnapchatStatus;
        }

        public void setShareSnapchatStatus(int shareSnapchatStatus) {
            this.shareSnapchatStatus = shareSnapchatStatus;
        }

        public int getOnMic5Status() {
            return onMic5Status;
        }

        public void setOnMic5Status(int onMic5Status) {
            this.onMic5Status = onMic5Status;
        }

        public int getSendGiftDiamond() {
            return sendGiftDiamond;
        }

        public void setSendGiftDiamond(int sendGiftDiamond) {
            this.sendGiftDiamond = sendGiftDiamond;
        }

        public int getReceiveGiftDiamond() {
            return receiveGiftDiamond;
        }

        public void setReceiveGiftDiamond(int receiveGiftDiamond) {
            this.receiveGiftDiamond = receiveGiftDiamond;
        }

        public int getRechargeTimes() {
            return rechargeTimes;
        }

        public void setRechargeTimes(int rechargeTimes) {
            this.rechargeTimes = rechargeTimes;
        }

        public int getInviteNewUser() {
            return inviteNewUser;
        }

        public void setInviteNewUser(int inviteNewUser) {
            this.inviteNewUser = inviteNewUser;
        }

        public int getInviteOldUser() {
            return inviteOldUser;
        }

        public void setInviteOldUser(int inviteOldUser) {
            this.inviteOldUser = inviteOldUser;
        }
    }

    public static class MyInfoVO extends MyInfo{



    }
    public static class PetInfoVO extends PetInfo{
        private int ageStage; // 年龄阶段 0幼年 2成年
        private int needFood; // 需要喂养的最大食物数量
        private int needToys; // 需要玩耍的最大玩具数量

        public int getAgeStage() {
            return ageStage;
        }
        public void setAgeStage(int ageStage) {
            this.ageStage = ageStage;
        }
        public int getNeedFood() {
            return needFood;
        }
        public void setNeedFood(int needFood) {
            this.needFood = needFood;
        }
        public int getNeedToys() {
            return needToys;
        }
        public void setNeedToys(int needToys) {
            this.needToys = needToys;
        }
    }

    public static class HistoryRecordPageVO{
        private List<HistoryRecordVO> historyRecordList;
        private int nextPage;

        public List<HistoryRecordVO> getHistoryRecordList() {
            return historyRecordList;
        }

        public void setHistoryRecordList(List<HistoryRecordVO> historyRecordList) {
            this.historyRecordList = historyRecordList;
        }

        public int getNextPage() {
            return nextPage;
        }

        public void setNextPage(int nextPage) {
            this.nextPage = nextPage;
        }
    }

    public static class HistoryRecordVO {
        // 1 喂食 2 投球 3 喂养他人 4 喂养他人达成目标 5 投球他人 6 投球他人达成目标 7 商店兑换 8 别人喂养我的宠物 9 别人投球我的宠物
        private int actionType;
        private int num; // 相应行为的数量
        private String aid; // 目标用户
        private int love; // 相应行为产生的爱心数量
        private int ctime; // 创建时间

        public int getActionType() {
            return actionType;
        }

        public void setActionType(int actionType) {
            this.actionType = actionType;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public int getLove() {
            return love;
        }

        public void setLove(int love) {
            this.love = love;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }
    }
}
