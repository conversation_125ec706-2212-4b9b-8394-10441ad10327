package com.quhong.data.vo;


import java.util.List;

public class WorldCupBetRecordVO {

    private List<BetRecord> betRecordList;
    private Integer nextUrl;

    public static class BetRecord {
        private String matchType;
        private Integer teamKey;
        private String teamIcon;
        private Integer betNum;
        private Integer winType;  // -1 未开奖  0 未中奖  1中奖
        private Integer winNum;
        private Integer ctime;

        public String getMatchType() {
            return matchType;
        }

        public void setMatchType(String matchType) {
            this.matchType = matchType;
        }

        public Integer getTeamKey() {
            return teamKey;
        }

        public void setTeamKey(Integer teamKey) {
            this.teamKey = teamKey;
        }

        public String getTeamIcon() {
            return teamIcon;
        }

        public void setTeamIcon(String teamIcon) {
            this.teamIcon = teamIcon;
        }

        public Integer getBetNum() {
            return betNum;
        }

        public void setBetNum(Integer betNum) {
            this.betNum = betNum;
        }

        public Integer getWinType() {
            return winType;
        }

        public void setWinType(Integer winType) {
            this.winType = winType;
        }

        public Integer getWinNum() {
            return winNum;
        }

        public void setWinNum(Integer winNum) {
            this.winNum = winNum;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }
    }

    public List<BetRecord> getBetRecordList() {
        return betRecordList;
    }

    public void setBetRecordList(List<BetRecord> betRecordList) {
        this.betRecordList = betRecordList;
    }

    public Integer getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(Integer nextUrl) {
        this.nextUrl = nextUrl;
    }
}
