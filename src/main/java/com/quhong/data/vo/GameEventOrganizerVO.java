package com.quhong.data.vo;


import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class GameEventOrganizerVO extends OtherRankConfigVO {

    private ChallengeInfoVO challengeInfoVO;
    private MyGiftVO myGiftVO;
    private List<RankVO> totalRankList; // 总榜
    private List<RoomItemVO> newShiningList; // event-new房间活动
    private List<RoomItemVO> hotEventList; // event-popular房间活动
    private List<RoomItemVO> searchEventList; // event-search房间活动
    private ReportVO totalReportVO; // 报表总数据
    private List<ReportVO> dayReportVOList;// 报表每日数据
    private List<HistoryRedisData> historyRedisDataList; // 积分增减流水
    private List<AdminLogVO> adminList; // 房间管理员列表

    private List<ResourceKeyConfigData> resourceKeyDataList;//  题目全部答对的奖励资源列表

    public static class ChallengeInfo {
        private int point;
        private int gameCount; // 总数
        private int micCount; // 总数
        private int subCount; // 总数
        private int videoPoint; // 总数
        private int voteCount; // 总数
        private Map<String, Integer> levelMapCollect; // key等级 "1" 值为是否领取当前等级奖励  0未完成 1已完成未领取 2已完成已领取
        private Map<String, List<String>> levelMapAidList; // key等级 "1" 值为当前等级 已下发给的admin uid列表

        public int getPoint() {
            return point;
        }

        public void setPoint(int point) {
            this.point = point;
        }

        public Map<String, Integer> getLevelMapCollect() {
            return levelMapCollect;
        }

        public void setLevelMapCollect(Map<String, Integer> levelMapCollect) {
            this.levelMapCollect = levelMapCollect;
        }

        public Map<String, List<String>> getLevelMapAidList() {
            return levelMapAidList;
        }

        public void setLevelMapAidList(Map<String, List<String>> levelMapAidList) {
            this.levelMapAidList = levelMapAidList;
        }

        public int getGameCount() {
            return gameCount;
        }

        public void setGameCount(int gameCount) {
            this.gameCount = gameCount;
        }

        public int getMicCount() {
            return micCount;
        }

        public void setMicCount(int micCount) {
            this.micCount = micCount;
        }

        public int getSubCount() {
            return subCount;
        }

        public void setSubCount(int subCount) {
            this.subCount = subCount;
        }

        public int getVideoPoint() {
            return videoPoint;
        }

        public void setVideoPoint(int videoPoint) {
            this.videoPoint = videoPoint;
        }

        public int getVoteCount() {
            return voteCount;
        }

        public void setVoteCount(int voteCount) {
            this.voteCount = voteCount;
        }
    }

    public static class DetailInfo {
        private String dayStr;
        private int ctime;
        private int dayPoint;
        private int gameCount;
        private int newMicCount;
        private int oldMicCount;
        private int newSubCount;
        private int oldSubCount;
        private Set<String> micDevices;
        private Set<String> subDevices;
        private String preStartGameId; // 上一次的游戏id
        private int eventCount; // 创建event次数 报表用字段

        private int videoPoints; // video point
        private int voteCount; // 创建投票次数
        private int dominoCount; // 创建event次数 报表用字段

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }

        public int getDayPoint() {
            return dayPoint;
        }

        public void setDayPoint(int dayPoint) {
            this.dayPoint = dayPoint;
        }

        public int getGameCount() {
            return gameCount;
        }

        public void setGameCount(int gameCount) {
            this.gameCount = gameCount;
        }

        public String getDayStr() {
            return dayStr;
        }

        public void setDayStr(String dayStr) {
            this.dayStr = dayStr;
        }

        public int getNewMicCount() {
            return newMicCount;
        }

        public void setNewMicCount(int newMicCount) {
            this.newMicCount = newMicCount;
        }

        public int getOldMicCount() {
            return oldMicCount;
        }

        public void setOldMicCount(int oldMicCount) {
            this.oldMicCount = oldMicCount;
        }

        public int getNewSubCount() {
            return newSubCount;
        }

        public void setNewSubCount(int newSubCount) {
            this.newSubCount = newSubCount;
        }

        public int getOldSubCount() {
            return oldSubCount;
        }

        public void setOldSubCount(int oldSubCount) {
            this.oldSubCount = oldSubCount;
        }

        public Set<String> getMicDevices() {
            return micDevices;
        }

        public void setMicDevices(Set<String> micDevices) {
            this.micDevices = micDevices;
        }

        public Set<String> getSubDevices() {
            return subDevices;
        }

        public void setSubDevices(Set<String> subDevices) {
            this.subDevices = subDevices;
        }

        public int getEventCount() {
            return eventCount;
        }

        public void setEventCount(int eventCount) {
            this.eventCount = eventCount;
        }

        public String getPreStartGameId() {
            return preStartGameId;
        }

        public void setPreStartGameId(String preStartGameId) {
            this.preStartGameId = preStartGameId;
        }

        public int getVideoPoints() {
            return videoPoints;
        }

        public void setVideoPoints(int videoPoints) {
            this.videoPoints = videoPoints;
        }

        public int getVoteCount() {
            return voteCount;
        }

        public void setVoteCount(int voteCount) {
            this.voteCount = voteCount;
        }

        public int getDominoCount() {
            return dominoCount;
        }

        public void setDominoCount(int dominoCount) {
            this.dominoCount = dominoCount;
        }
    }

    public static class MyGiftDetailInfo {
        private String dayStr;
        private Set<String> subEventIds;  // 每天订阅活动场次
        private Set<String> micEventIds; // 每天参与活动场次
        private int subState; // 0未完成未领取  1 已完成未领取  2 已完成已领取
        private int micState; // 0未完成未领取  1 已完成未领取  2 已完成已领取
        private int quizState; // 用户当天答题的状态 0 未答题 1已答题

        public Set<String> getSubEventIds() {
            return subEventIds;
        }

        public void setSubEventIds(Set<String> subEventIds) {
            this.subEventIds = subEventIds;
        }

        public Set<String> getMicEventIds() {
            return micEventIds;
        }

        public void setMicEventIds(Set<String> micEventIds) {
            this.micEventIds = micEventIds;
        }

        public String getDayStr() {
            return dayStr;
        }

        public void setDayStr(String dayStr) {
            this.dayStr = dayStr;
        }

        public int getSubState() {
            return subState;
        }

        public void setSubState(int subState) {
            this.subState = subState;
        }

        public int getMicState() {
            return micState;
        }

        public void setMicState(int micState) {
            this.micState = micState;
        }

        public int getQuizState() {
            return quizState;
        }

        public void setQuizState(int quizState) {
            this.quizState = quizState;
        }
    }


    public static class ChallengeInfoVO {
        private int videoPoint;
        private int voteCount;
        private int gameCount;
        private int micCount;  // 总的
        private int subCount;
        private int point;
        private int leftLevel;
        private int needPoint;
        private int process; // 0-100;
        private List<LevelVO> levelVOList;

        public int getGameCount() {
            return gameCount;
        }

        public void setGameCount(int gameCount) {
            this.gameCount = gameCount;
        }

        public int getMicCount() {
            return micCount;
        }

        public void setMicCount(int micCount) {
            this.micCount = micCount;
        }

        public int getSubCount() {
            return subCount;
        }

        public void setSubCount(int subCount) {
            this.subCount = subCount;
        }

        public int getPoint() {
            return point;
        }

        public void setPoint(int point) {
            this.point = point;
        }

        public int getLeftLevel() {
            return leftLevel;
        }

        public void setLeftLevel(int leftLevel) {
            this.leftLevel = leftLevel;
        }

        public int getNeedPoint() {
            return needPoint;
        }

        public void setNeedPoint(int needPoint) {
            this.needPoint = needPoint;
        }

        public int getProcess() {
            return process;
        }

        public void setProcess(int process) {
            this.process = process;
        }

        public List<LevelVO> getLevelVOList() {
            return levelVOList;
        }

        public void setLevelVOList(List<LevelVO> levelVOList) {
            this.levelVOList = levelVOList;
        }

        public int getVideoPoint() {
            return videoPoint;
        }

        public void setVideoPoint(int videoPoint) {
            this.videoPoint = videoPoint;
        }

        public int getVoteCount() {
            return voteCount;
        }

        public void setVoteCount(int voteCount) {
            this.voteCount = voteCount;
        }
    }

    public static class LevelVO {
        private int level;
        private int state; // 0 未完成 1,2 已完成
        private int roomOwnerState; // 0未完成未领取  1 已完成未领取  2 已完成已领取
        private int roomAdminState; // 0未完成未领取  1 已完成未领取  2 已完成已领取
        private List<String> adminGetList; // 发放记录admin头像
        private int levelScore; //当前等级需要的积分

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public int getState() {
            return state;
        }

        public void setState(int state) {
            this.state = state;
        }

        public int getRoomOwnerState() {
            return roomOwnerState;
        }

        public void setRoomOwnerState(int roomOwnerState) {
            this.roomOwnerState = roomOwnerState;
        }

        public int getRoomAdminState() {
            return roomAdminState;
        }

        public void setRoomAdminState(int roomAdminState) {
            this.roomAdminState = roomAdminState;
        }

        public List<String> getAdminGetList() {
            return adminGetList;
        }

        public void setAdminGetList(List<String> adminGetList) {
            this.adminGetList = adminGetList;
        }

        public int getLevelScore() {
            return levelScore;
        }

        public void setLevelScore(int levelScore) {
            this.levelScore = levelScore;
        }
    }

    public static class MyGiftVO {
        private int subCount;  // 每天订阅3场活动
        private int micCount; // 每天参与3场活动进入派对且上麦时长≥1分钟
        private int subState; // 0未完成未领取  1 已完成未领取  2 已完成已领取
        private int micState; // 0未完成未领取  1 已完成未领取  2 已完成已领取

        public int getSubCount() {
            return subCount;
        }

        public void setSubCount(int subCount) {
            this.subCount = subCount;
        }

        public int getMicCount() {
            return micCount;
        }

        public void setMicCount(int micCount) {
            this.micCount = micCount;
        }

        public int getSubState() {
            return subState;
        }

        public void setSubState(int subState) {
            this.subState = subState;
        }

        public int getMicState() {
            return micState;
        }

        public void setMicState(int micState) {
            this.micState = micState;
        }
    }

    public static class RankVO {
        private String hostHead;
        private String hostName;
        private String countryFlag;
        private Integer rank;
        private Integer points;
        private String roomId;

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public String getHostHead() {
            return hostHead;
        }

        public void setHostHead(String hostHead) {
            this.hostHead = hostHead;
        }

        public String getHostName() {
            return hostName;
        }

        public void setHostName(String hostName) {
            this.hostName = hostName;
        }


        public String getCountryFlag() {
            return countryFlag;
        }

        public void setCountryFlag(String countryFlag) {
            this.countryFlag = countryFlag;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public Integer getPoints() {
            return points;
        }

        public void setPoints(Integer points) {
            this.points = points;
        }
    }

    public static class RoomItemVO {
        private String roomId;

        // new
        private String roomEventCover; //房间活动封面
        private Integer roomEventState; // 房间活动状态 0待开始 1进行中
        private Integer roomEventStartTime; // 活动开始时间
        private String roomEventName; // 活动名称
        private Integer roomEventSubCount; // 订阅人数
        private Integer roomEventSubState; // 房间活动订阅状态 0未订阅 1已订阅
        private Integer roomEventId; // 房间活动id

        // hot
        private String roomCover; //房间封面
        private Integer roomOnline; // 房间人数
        private String countryFlag;
        private String roomName;
        private Integer points; // 房间积分
        private String hostHead;
        private String mangerHead;
        private Integer roomLevel;
        private String roomTypeIcon;
        private String roomTypeName;
        private String roomLevelIcon;
        private Integer rank; // 活动排名

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public String getRoomTypeIcon() {
            return roomTypeIcon;
        }

        public void setRoomTypeIcon(String roomTypeIcon) {
            this.roomTypeIcon = roomTypeIcon;
        }

        public String getRoomTypeName() {
            return roomTypeName;
        }

        public void setRoomTypeName(String roomTypeName) {
            this.roomTypeName = roomTypeName;
        }

        public String getRoomCover() {
            return roomCover;
        }

        public void setRoomCover(String roomCover) {
            this.roomCover = roomCover;
        }

        public Integer getRoomOnline() {
            return roomOnline;
        }

        public void setRoomOnline(Integer roomOnline) {
            this.roomOnline = roomOnline;
        }

        public String getCountryFlag() {
            return countryFlag;
        }

        public void setCountryFlag(String countryFlag) {
            this.countryFlag = countryFlag;
        }

        public String getRoomName() {
            return roomName;
        }

        public void setRoomName(String roomName) {
            this.roomName = roomName;
        }

        public Integer getPoints() {
            return points;
        }

        public void setPoints(Integer points) {
            this.points = points;
        }

        public String getHostHead() {
            return hostHead;
        }

        public void setHostHead(String hostHead) {
            this.hostHead = hostHead;
        }

        public String getMangerHead() {
            return mangerHead;
        }

        public void setMangerHead(String mangerHead) {
            this.mangerHead = mangerHead;
        }

        public Integer getRoomLevel() {
            return roomLevel;
        }

        public void setRoomLevel(Integer roomLevel) {
            this.roomLevel = roomLevel;
        }

        public String getRoomEventCover() {
            return roomEventCover;
        }

        public void setRoomEventCover(String roomEventCover) {
            this.roomEventCover = roomEventCover;
        }

        public Integer getRoomEventState() {
            return roomEventState;
        }

        public void setRoomEventState(Integer roomEventState) {
            this.roomEventState = roomEventState;
        }

        public Integer getRoomEventStartTime() {
            return roomEventStartTime;
        }

        public void setRoomEventStartTime(Integer roomEventStartTime) {
            this.roomEventStartTime = roomEventStartTime;
        }

        public String getRoomEventName() {
            return roomEventName;
        }

        public void setRoomEventName(String roomEventName) {
            this.roomEventName = roomEventName;
        }

        public Integer getRoomEventSubCount() {
            return roomEventSubCount;
        }

        public void setRoomEventSubCount(Integer roomEventSubCount) {
            this.roomEventSubCount = roomEventSubCount;
        }

        public String getRoomLevelIcon() {
            return roomLevelIcon;
        }

        public void setRoomLevelIcon(String roomLevelIcon) {
            this.roomLevelIcon = roomLevelIcon;
        }

        public Integer getRoomEventSubState() {
            return roomEventSubState;
        }

        public void setRoomEventSubState(Integer roomEventSubState) {
            this.roomEventSubState = roomEventSubState;
        }

        public Integer getRoomEventId() {
            return roomEventId;
        }

        public void setRoomEventId(Integer roomEventId) {
            this.roomEventId = roomEventId;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }
    }

    public static class HistoryRedisData {
        private int ctime;
        private String key;
        private int change; //

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }

        public int getChange() {
            return change;
        }

        public void setChange(int change) {
            this.change = change;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }

    public static class ReportVO {
        private String day;
        private int createEventCount;
        private int subCount;
        private int micCount;

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }

        public int getCreateEventCount() {
            return createEventCount;
        }

        public void setCreateEventCount(int createEventCount) {
            this.createEventCount = createEventCount;
        }

        public int getSubCount() {
            return subCount;
        }

        public void setSubCount(int subCount) {
            this.subCount = subCount;
        }

        public int getMicCount() {
            return micCount;
        }

        public void setMicCount(int micCount) {
            this.micCount = micCount;
        }
    }

    public static class AdminLogVO {
        private String aid;
        private String head;
        private String name;
        private int state; // 赠送状态 1已赠送 ，0未赠送

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getState() {
            return state;
        }

        public void setState(int state) {
            this.state = state;
        }
    }

    public ChallengeInfoVO getChallengeInfoVO() {
        return challengeInfoVO;
    }

    public void setChallengeInfoVO(ChallengeInfoVO challengeInfoVO) {
        this.challengeInfoVO = challengeInfoVO;
    }

    public MyGiftVO getMyGiftVO() {
        return myGiftVO;
    }

    public void setMyGiftVO(MyGiftVO myGiftVO) {
        this.myGiftVO = myGiftVO;
    }

    public List<RankVO> getTotalRankList() {
        return totalRankList;
    }

    public void setTotalRankList(List<RankVO> totalRankList) {
        this.totalRankList = totalRankList;
    }

    public List<RoomItemVO> getNewShiningList() {
        return newShiningList;
    }

    public void setNewShiningList(List<RoomItemVO> newShiningList) {
        this.newShiningList = newShiningList;
    }

    public List<RoomItemVO> getHotEventList() {
        return hotEventList;
    }

    public void setHotEventList(List<RoomItemVO> hotEventList) {
        this.hotEventList = hotEventList;
    }

    public List<HistoryRedisData> getHistoryRedisDataList() {
        return historyRedisDataList;
    }

    public void setHistoryRedisDataList(List<HistoryRedisData> historyRedisDataList) {
        this.historyRedisDataList = historyRedisDataList;
    }

    public ReportVO getTotalReportVO() {
        return totalReportVO;
    }

    public void setTotalReportVO(ReportVO totalReportVO) {
        this.totalReportVO = totalReportVO;
    }

    public List<ReportVO> getDayReportVOList() {
        return dayReportVOList;
    }

    public void setDayReportVOList(List<ReportVO> dayReportVOList) {
        this.dayReportVOList = dayReportVOList;
    }

    public List<AdminLogVO> getAdminList() {
        return adminList;
    }

    public void setAdminList(List<AdminLogVO> adminList) {
        this.adminList = adminList;
    }

    public List<RoomItemVO> getSearchEventList() {
        return searchEventList;
    }

    public void setSearchEventList(List<RoomItemVO> searchEventList) {
        this.searchEventList = searchEventList;
    }

    public List<ResourceKeyConfigData> getResourceKeyDataList() {
        return resourceKeyDataList;
    }

    public void setResourceKeyDataList(List<ResourceKeyConfigData> resourceKeyDataList) {
        this.resourceKeyDataList = resourceKeyDataList;
    }
}
