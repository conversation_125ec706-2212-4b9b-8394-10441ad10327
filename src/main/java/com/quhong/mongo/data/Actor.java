package com.quhong.mongo.data;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.List;

/**
 * 用户对象
 */
@Document(collection = "actor")
public class Actor implements Serializable {

    private static final long serialVersionUID = 4399361100976103839L;

    @Id
    private ObjectId _id;
    private int beans;
    private int rid;
    private String name;
    private int gender;
    @Field("fb_gender")
    private int fbGender;
    private String head;
    private String os;
    private int age;
    @Field("heart_got")
    private int heartGot;
    @Field("heart_received")
    private int heartReceived;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getBeans() {
        return beans;
    }

    public void setBeans(int beans) {
        this.beans = beans;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getFbGender() {
        return fbGender;
    }

    public void setFbGender(int fbGender) {
        this.fbGender = fbGender;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public int getHeartGot() {
        return heartGot;
    }

    public void setHeartGot(int heartGot) {
        this.heartGot = heartGot;
    }

    public int getHeartReceived() {
        return heartReceived;
    }

    public void setHeartReceived(int heartReceived) {
        this.heartReceived = heartReceived;
    }
}
