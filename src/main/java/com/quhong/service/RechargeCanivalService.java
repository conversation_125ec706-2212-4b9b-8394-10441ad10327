package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.RechargeInfo;
import com.quhong.data.vo.RechargeCanivalVO;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.ArithmeticUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 202406充值活动
 */
@Service
public class RechargeCanivalService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(RechargeCanivalService.class);
    private static final String ACTIVITY_TITLE_EN = "Recharge Canival Activity";
    private static final String ACTIVITY_TITLE_AR = "إعادة شحن كرنفال";
    private static final String ACTIVITY_ID = "689bf207bd672167d5865d59";//
    private static final String ACTIVITY_RECHARGE_REWARD_KEY = "rechargeDailyReward";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/rechargeCanival2025_08/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/rechargeCanival2025_08/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "https://cdn3.qmovies.tv/youstar/op_1727265048_fjrk.png";
    private static final List<Double> MONEY_LEVEL_LIST = Arrays.asList(0.0, 0.99, 5.98, 20.98, 49.99, 69.99, 199.99, 599.99, 899.99, 1399.99, 1799.99, 2599.99, 3999.99, 5699.99);
    private static final List<String> RES_LEVEL_LIST = Arrays.asList("", "recharge202406Level0#99", "recharge202406Level4#99",
            "recharge202406Level19#99", "recharge202406Level49#99", "recharge202406Level69#99", "recharge202406Level199#99", "recharge202410Level599#99",
            "recharge202406Level899#99", "recharge202406Level1399#99", "recharge202406Level1799#99", "recharge202406Level2599#99", "recharge202406Level3999#99", "recharge202406Level4999#99");

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;

    private String getDailyHashActivityId(String activityId, String uid) {
        return String.format("dailyRechargeConfig:%s:%s", activityId, uid);
    }

    // 记录每日是否充值金额
    private String getZSetDailyRechargeActivityId(String activityId, String dateStr) {
        return String.format("dailyRecharge:%s:%s", activityId, dateStr);
    }

    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }


    public RechargeCanivalVO rechargeCanivalConfig(String activityId, String uid) {
        RechargeCanivalVO vo = new RechargeCanivalVO();
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        double nowMoney = ArithmeticUtils.round(activityCommonRedis.getCommonZSetRankingDoubleScore(activityId, uid), 2);
        int maxIndexLevel = MONEY_LEVEL_LIST.size();
        int currentLevelIndex = getIndexLevel(nowMoney);
        double levelMoney = MONEY_LEVEL_LIST.get(currentLevelIndex);
        String nextAddMoney = "";
        if (currentLevelIndex < maxIndexLevel - 1) {
            double nextLevelMoney = MONEY_LEVEL_LIST.get(currentLevelIndex + 1);
            nextAddMoney = ArithmeticUtils.sub(nextLevelMoney + "", nowMoney + "", 2);
        }

        String currentDay = DateHelper.ARABIAN.formatDateInDay();
        // String currentDay = activityCommonRedis.getCommonStrValue(this.getDailyDate(ACTIVITY_ID));

        String dailyRechargeKey = getZSetDailyRechargeActivityId(activityId, currentDay);
        double dailyRechargeMoney = ArithmeticUtils.round(activityCommonRedis.getCommonZSetRankingDoubleScore(dailyRechargeKey, uid), 2);
        vo.setDailyRecharge(String.valueOf(dailyRechargeMoney));

        vo.setNowMoney(String.valueOf(nowMoney));
        vo.setNextMoney(nextAddMoney);
        vo.setLevelMoney(String.valueOf(levelMoney));
        return vo;
    }

    public void handleUserRecharge(RechargeInfo rechargeInfo) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
        if (activityData == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        String uid = rechargeInfo.getUid();

        if (activityData.getAcNameEn().startsWith("test") && ServerConfig.isProduct() && !whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID)) {
            // 灰度测试
            return;
        }
        double money = rechargeInfo.getRechargeMoney() == null ? 0 : rechargeInfo.getRechargeMoney();
        if (money > 0) {
            double old = activityCommonRedis.getCommonZSetRankingDoubleScore(ACTIVITY_ID, uid);
            double after = activityCommonRedis.incrCommonZSetRankingScoreDouble(ACTIVITY_ID, uid, money);
            int oldIndex = getIndexLevel(old);
            int afterIndex = getIndexLevel(after);
            logger.info("uid:{} money:{} old:{} after:{} oldIndex:{} afterIndex:{}", uid, money, old, after, oldIndex, afterIndex);
            if (afterIndex > oldIndex) {
                for (int i = oldIndex + 1; i <= afterIndex; i++) {
                    String resKey = RES_LEVEL_LIST.get(i);
                    logger.info("uid:{} index:{} resKey:{} ", uid, i, resKey);
                    resourceKeyHandlerService.sendResourceData(uid, resKey,
                            ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_TITLE_EN, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                }
            }

            String currentDay = DateHelper.ARABIAN.formatDateInDay();
            // String currentDay = activityCommonRedis.getCommonStrValue(this.getDailyDate(ACTIVITY_ID));
            // 每日充值金额
            String dailyRechargeKey = getZSetDailyRechargeActivityId(ACTIVITY_ID, currentDay);
            double dailyAfterRecharge = activityCommonRedis.incrCommonZSetRankingScoreDouble(dailyRechargeKey, uid, money);

            String hashActivityId = this.getDailyHashActivityId(ACTIVITY_ID, uid);
            Map<String, Integer> dailyRechargeMap = activityCommonRedis.getCommonHashAll(hashActivityId);
            int dailyTaskStatus = dailyRechargeMap.getOrDefault(currentDay, 0);  // 当天是否下发过每日任务奖励
            if (dailyTaskStatus <= 0 && dailyAfterRecharge >= 4.99) {
                activityCommonRedis.incCommonHashNum(hashActivityId, currentDay, 1);
                resourceKeyHandlerService.sendResourceData(uid, ACTIVITY_RECHARGE_REWARD_KEY, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_TITLE_EN, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
            }
        }
    }

    private int getIndexLevel(double score) {
        List<Double> tempLevelNumList = new ArrayList<>(MONEY_LEVEL_LIST);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Double::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }
}
