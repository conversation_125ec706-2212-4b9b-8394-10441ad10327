package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.RechargeInfo;
import com.quhong.data.vo.RechargeCanivalVO;
import com.quhong.data.vo.LimitedRechargeVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.SLangType;
import com.quhong.handler.ActivityRechargeHandler;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.UserRechargeRecordDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.ArithmeticUtils;
import com.quhong.utils.ActorUtils;
import com.quhong.room.RoomWebSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 限时充值福利
 */
@Service
public class LimitedRechargeBenefitService extends OtherActivityService implements ActivityRechargeHandler, TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(LimitedRechargeBenefitService.class);
    private static final String ACTIVITY_TITLE_EN = "limited recharge benefit";
    private static final String ACTIVITY_ID = "6855285533fa3d1db9922591";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/limited_recharge/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/limited_recharge/?activityId=%s", ACTIVITY_ID);
    private static final List<Double> MONEY_LEVEL_LIST = Arrays.asList(0.0, 4.99, 24.99, 69.99, 129.99);
    private static final List<String> RES_LEVEL_LIST = Arrays.asList("", "limitoffer499", "limitoffer2499", "limitoffer6999", "limitoffer12999");
    private static final List<String> EVENT_LEVEL_LIST = Arrays.asList("", "First Charge Discount-reward1", "First Charge Discount-reward2", "First Charge Discount-reward3", "First Charge Discount-reward4");
    private static final List<String> FORMAL_RECHARGE_CHANNEL = Arrays.asList("google", "apple", "huawei");
    private static final List<String> TASK_ITEM_LIST = Arrays.asList(CommonMqTaskConstant.DAILY_LOGIN);

    // 首充用户充值优惠相关常量
    private static final String LIMITED_RECHARGE_KEY = "limited_recharge_benefit";
    private static final String PUSH_STATUS_KEY = "limited_recharge_push_status";
    private static final String REGISTER_ONE_DAY_PUSH_FIELD = "registerOneDayPush";
    private static final String REGISTER_TWO_DAY_PUSH_FIELD = "registerTwoDayPush";
    private static final String FIRST_RECHARGE_URL =  ServerConfig.isProduct() ? "https://static.youstar.live/RechargeReward/" : "https://test2.qmovies.tv/RechargeReward/";
    private static  int FIRST_RECHARGE_VALID_HOURS = 168; // 168小时
    private static  int TOPIC_RID = 16251;

    // 消息文本常量 - 注册第1天推送
    private static final String FIRST_RECHARGE_OFFER_TITLE_EN = "First Recharge Gift Pack";
    private static final String FIRST_RECHARGE_OFFER_TITLE_AR = "حزمة هدايا إعادة الشحن الأولى";
    private static final String FIRST_RECHARGE_OFFER_BODY_EN = "The first recharge can enjoy up to 30% extra diamonds and permanent medals, check out the details";
    private static final String FIRST_RECHARGE_OFFER_BODY_AR = " احصل على ما يصل إلى 30% من الماس الإضافي والوسام الدائم عند الشحن الأول، تحقق من التفاصيل الآن";
    private static final String FIRST_RECHARGE_OFFER_PICTURE = "https://cdn3.qmovies.tv/youstar/op_1753947100_FIRST_RECHARGE_OFFER2.jpg";

    // 消息文本常量 - 注册第2天推送
    private static final String RICH_RECHARGE_GIFT_TITLE_EN = "Limited-Time Recharge Offer";
    private static final String RICH_RECHARGE_GIFT_TITLE_AR = "عرض شحن لفترة محدودة";
    private static final String RICH_RECHARGE_GIFT_BODY_EN = "Generous recharge gift packages are waiting for you to unlock.";
    private static final String RICH_RECHARGE_GIFT_BODY_AR = " حزم هدايا الشحن السخية في انتظارك لفتحها.";
    private static final String RICH_RECHARGE_GIFT_PICTURE = "https://cdn3.qmovies.tv/youstar/op_1753867672_RICH_RECHARGE_GIFT.jpg";

    // 消息文本常量 - 首笔充值解锁
    private static final String LIMITED_RECHARGE_UNLOCKED_TITLE_EN = "Congratulations on unlocking the limited-time recharge offer. Check out the details now.";
    private static final String LIMITED_RECHARGE_UNLOCKED_TITLE_AR = " مبروك على فتح خصم الشحن لفترة محدودة. تحقق من التفاصيل الآن.";
    private static final String LIMITED_RECHARGE_UNLOCKED_BODY_EN = "Congratulations on unlocking the limited-time recharge offer. Check out the details now.";
    private static final String LIMITED_RECHARGE_UNLOCKED_BODY_AR = " مبروك على فتح خصم الشحن لفترة محدودة. تحقق من التفاصيل الآن.";

    // 消息文本常量 - 奖励发放
    private static final String RECHARGE_REWARD_DISTRIBUTED_TITLE_EN = "The recharge reward has been sent, go check your backpack.";
    private static final String RECHARGE_REWARD_DISTRIBUTED_TITLE_AR = "تم توزيع مكافأة الشحن، تحقق من حقيبتك الآن" ;
    private static final String RECHARGE_REWARD_DISTRIBUTED_BODY_EN = "The recharge reward has been sent, go check your backpack.";
    private static final String RECHARGE_REWARD_DISTRIBUTED_BODY_AR = "تم توزيع مكافأة الشحن، تحقق من حقيبتك الآن";

    // 消息文本常量 - 注册第1天推送话题
    private static final String NEW_USER_OFFER_TITLE_EN = "Share your first moment now! \uD83D\uDCF8✨";
    private static final String NEW_USER_OFFER_TITLE_AR = "شارك أول لحظة لك الآن! \uD83D\uDCF8✨";
    private static final String NEW_USER_OFFER_BODY_EN = "Don't hesitate, share a photo or a simple idea in our topic #I_am_new_let's_get_to_know_you \uD83D\uDCAC\uD83E\uDD17. Everyone here is waiting to get to know you! \uD83D\uDC40\uD83E\uDD1D";
    private static final String NEW_USER_OFFER_BODY_AR = "لا تتردد، شارك صورة أو فكرة بسيطة في موضوعنا #أنا_جديد_فلنتعرف \uD83D\uDCAC\uD83E\uDD17. الجميع هنا ينتظر التعرف عليك! \uD83D\uDC40\uD83E\uDD1D";
    private static final String NEW_USER_OFFER_PICTURE = "https://cdn3.qmovies.tv/youstar/op_1755157418_tapd_20792731_base64_1753950931_810.png";


    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private UserRechargeRecordDao userRechargeRecordDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private RoomWebSender roomWebSender;


    // 获取首充用户充值优惠Redis key
    private String getLimitedRechargeKey() {
        return LIMITED_RECHARGE_KEY;
    }
    
    // 获取推送状态Redis key
    private String getPushStatusKey(String uid) {
        return PUSH_STATUS_KEY + ":" + uid;
    }

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            FIRST_RECHARGE_VALID_HOURS = 1;
            TOPIC_RID = 10132;
        }
    }

    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();
        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);

        if (activityData == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }

        if (activityData.getAcNameEn().startsWith("test") && ServerConfig.isProduct() && !whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID)) {
            // 灰度测试
            return;
        }
        dailyLoginHandle(data);
    }

    @Override
    public void process(RechargeInfo rechargeInfo) {
        logger.info("limitedRechargeBenefitService process rechargeInfo{}", rechargeInfo);
        if (rechargeInfo == null || !StringUtils.hasLength(rechargeInfo.getUid()) || rechargeInfo.getRechargeMoney() == null) {
            return;
        }
        int rechargeType = rechargeInfo.getRechargeType();
        if (rechargeType != 1 || !FORMAL_RECHARGE_CHANNEL.contains(rechargeInfo.getRechargeItem())) {
            return;
        }
        handleUserRecharge(rechargeInfo);
    }

    public LimitedRechargeVO limitedRechargeConfig(String activityId, String uid) {
        LimitedRechargeVO vo = new LimitedRechargeVO();
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        LimitedRechargeVO.LimitedRechargeInfo limitedRechargeInfo = getLimitedRechargeInfo(getLimitedRechargeKey(), uid);
        if (limitedRechargeInfo.getFirstRechargeTime() > 0) {
            vo.setRechargeEndTime(limitedRechargeInfo.getFirstRechargeTime() + FIRST_RECHARGE_VALID_HOURS * 3600);
            vo.setNowMoney(String.valueOf(limitedRechargeInfo.getTotalRechargeMoney()));
        } else {
            vo.setRechargeEndTime(0);
            vo.setNowMoney("0");
        }
        return vo;
    }

    public void dailyLoginHandle(CommonMqTopicData data) {
        String uid = data.getUid();
        if (!StringUtils.hasLength(uid)) {
            return;
        }

        // 获取用户推送状态，使用缓存
        String pushStatusKey = getPushStatusKey(uid);

        // 注册第1天用户启动app立即推送系统消息
        if (ActorUtils.isNewRegisterActor(uid, 1)) {
            String registerOneDayValue = cacheDataService.getPushFieldCache(pushStatusKey, REGISTER_ONE_DAY_PUSH_FIELD);
            if (!"1".equals(registerOneDayValue)) {
                sendOfficialMsg(uid, FIRST_RECHARGE_OFFER_PICTURE,FIRST_RECHARGE_OFFER_TITLE_EN, FIRST_RECHARGE_OFFER_TITLE_AR,
                        FIRST_RECHARGE_OFFER_BODY_EN, FIRST_RECHARGE_OFFER_BODY_AR, FIRST_RECHARGE_URL);
                if (ServerConfig.isNotProduct()) {
                    sendOfficialMsg(uid, NEW_USER_OFFER_PICTURE, NEW_USER_OFFER_TITLE_EN, NEW_USER_OFFER_TITLE_AR,
                            NEW_USER_OFFER_BODY_EN, NEW_USER_OFFER_BODY_AR,"", String.valueOf(TOPIC_RID),29);
                }
                // 更新推送状态
                updatePushField(pushStatusKey, REGISTER_ONE_DAY_PUSH_FIELD, "1");
                logger.info("dailyLoginHandle register day 1 push sent to uid: {}", uid);
            }
        }

        // 注册第2天用户启动app立即推送系统消息
        if (ActorUtils.isNewRegisterActor(uid, 2)) {
            String registerTwoDayValue = cacheDataService.getPushFieldCache(pushStatusKey, REGISTER_TWO_DAY_PUSH_FIELD);
            if (!"1".equals(registerTwoDayValue)) {
                sendOfficialMsg(uid,RICH_RECHARGE_GIFT_PICTURE, RICH_RECHARGE_GIFT_TITLE_EN, RICH_RECHARGE_GIFT_TITLE_AR,
                        RICH_RECHARGE_GIFT_BODY_EN, RICH_RECHARGE_GIFT_BODY_AR, ACTIVITY_URL);
                
                // 更新推送状态
                updatePushField(pushStatusKey, REGISTER_TWO_DAY_PUSH_FIELD, "1");
                logger.info("dailyLoginHandle register day 2 push sent to uid: {}", uid);
            }
        }
    }

    /**
     * 更新推送字段到Redis并清除缓存
     */
    private void updatePushField(String pushStatusKey, String field, String value) {
        activityCommonRedis.setCommonHashData(pushStatusKey, field, value);
        // 清除缓存，确保下次获取到最新数据
        cacheDataService.delPushFieldCache(pushStatusKey, field);
    }

    public void handleUserRecharge(RechargeInfo rechargeInfo) {
        OtherRankingActivityData otherRankingActivityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
        if (otherRankingActivityData == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        String uid = rechargeInfo.getUid();
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
        if (activityData.getAcNameEn().startsWith("test") && ServerConfig.isProduct() && !whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID)) {
            // 灰度测试
            return;
        }
        double money = rechargeInfo.getRechargeMoney() == null ? 0 : rechargeInfo.getRechargeMoney();
        money = ArithmeticUtils.round(money, 2);
        // 获取用户充值信息
        String limitedRechargeKey = getLimitedRechargeKey();
        String infoJson = activityCommonRedis.getCommonHashStrValue(limitedRechargeKey, uid);
        LimitedRechargeVO.LimitedRechargeInfo info;
        boolean isFirstRecharge = false;

        if (StringUtils.hasLength(infoJson)) {
            info = JSONObject.parseObject(infoJson, LimitedRechargeVO.LimitedRechargeInfo.class);
        } else {
            info = new LimitedRechargeVO.LimitedRechargeInfo();
            isFirstRecharge = true;
        }

        int currentTime = DateHelper.getNowSeconds();
        double oldTotalMoney = info.getTotalRechargeMoney();
        // 如果是首次充值，记录首次充值时间
        if (isFirstRecharge) {
            info.setFirstRechargeTime(currentTime);
            info.setTotalRechargeMoney(money);

            // 完成首笔充值用户立即推送系统消息
            sendOfficialMsg(uid,"", LIMITED_RECHARGE_UNLOCKED_TITLE_EN, LIMITED_RECHARGE_UNLOCKED_TITLE_AR,
                    LIMITED_RECHARGE_UNLOCKED_BODY_EN, LIMITED_RECHARGE_UNLOCKED_BODY_AR, ACTIVITY_URL);
            logger.info("handleUserRecharge first recharge unlock message sent to uid: {}", uid);
        } else {
            // 检查是否在168小时内
            int firstRechargeTime = info.getFirstRechargeTime();
            int validEndTime = firstRechargeTime + FIRST_RECHARGE_VALID_HOURS * 3600;

            if (currentTime > validEndTime) {
                // 超过168小时，不再累积充值
//                logger.info("handleUserRecharge recharge expired for uid: {}, current: {}, valid end: {}", uid, currentTime, validEndTime);
                return;
            }

            // 累积充值金额
            info.setTotalRechargeMoney(ArithmeticUtils.add(oldTotalMoney, money));
        }

        // 检查是否达到奖励等级
        double totalMoney = info.getTotalRechargeMoney();
        int oldLevel = getCurrentLevel(oldTotalMoney);
        int newLevel = getCurrentLevel(totalMoney);

        // 如果等级提升，下发奖励
        if (newLevel > oldLevel) {
            // 跨级的，需要把之前等级的key都下发
            for (int level = oldLevel + 1; level <= newLevel; level++) {
                if (level < RES_LEVEL_LIST.size() && StringUtils.hasLength(RES_LEVEL_LIST.get(level))) {
                    String resKey = RES_LEVEL_LIST.get(level);
                    String eventTitle = EVENT_LEVEL_LIST.get(level);
                    resourceKeyHandlerService.sendResourceData(uid, resKey, eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
                    logger.info("handleUserRecharge reward sent to uid: {}, level: {}, resKey: {}", uid, level, resKey);
                }
            }

            // 发送奖励已发放的系统消息
            sendOfficialMsg(uid,"", RECHARGE_REWARD_DISTRIBUTED_TITLE_EN, RECHARGE_REWARD_DISTRIBUTED_TITLE_AR,
                    RECHARGE_REWARD_DISTRIBUTED_BODY_EN, RECHARGE_REWARD_DISTRIBUTED_BODY_AR, ACTIVITY_URL);
            logger.info("handleUserRecharge reward distribution message sent to uid: {}", uid);
        }

        // 保存更新后的信息
        activityCommonRedis.setCommonHashData(limitedRechargeKey, uid, JSONObject.toJSONString(info));
        // 清除缓存，确保下次获取到最新数据
        cacheDataService.delLimitedRechargeInfo(limitedRechargeKey, uid);
        logger.info("handleUserRecharge updated info for uid: {}, total money: {}, level: {}", uid, totalMoney, newLevel);
    }

    /**
     * 根据充值金额获取当前等级
     */
    private int getCurrentLevel(double money) {
        for (int i = MONEY_LEVEL_LIST.size() - 1; i >= 0; i--) {
            if (money >= MONEY_LEVEL_LIST.get(i)) {
                return i;
            }
        }
        return 0;
    }

    private void sendOfficialMsg(String uid,String picture, String titleEn, String titleAr, String bodyEn, String bodyAr, String url) {
        sendOfficialMsg(uid,picture, titleEn, titleAr, bodyEn, bodyAr, url,"",0);
    }

    /**
     * 发送系统消息
     */
    private void sendOfficialMsg(String uid,String picture, String titleEn, String titleAr, String bodyEn, String bodyAr, String url
            ,String actionId,int actionType) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("sendOfficialMsg actor not found for uid: {}", uid);
            return;
        }
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleAr : titleEn;
        String body = slang == SLangType.ARABIC ? bodyAr : bodyEn;
        String actText = slang == SLangType.ARABIC ? "شاهد" : "View";
        commonOfficialMsg(uid, picture, actionType, 0, actText, title, body, url,actionId);
    }

    private LimitedRechargeVO.LimitedRechargeInfo getLimitedRechargeInfo(String limitedRechargeKey, String uid) {
        LimitedRechargeVO.LimitedRechargeInfo info;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(limitedRechargeKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            info = JSONObject.parseObject(jsonValue, LimitedRechargeVO.LimitedRechargeInfo.class);
        } else {
            info = new LimitedRechargeVO.LimitedRechargeInfo();
        }
        return info;
    }

    private int getIndexLevel(double score) {
        List<Double> tempLevelNumList = new ArrayList<>(MONEY_LEVEL_LIST);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Double::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }
}
