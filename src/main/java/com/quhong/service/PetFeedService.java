package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.vo.PetFeedVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.TaskConfigVO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * 养宠活动
 */
@Service
public class PetFeedService extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(PetFeedService.class);
    private static final String ACTIVITY_TITLE_EN = "Pet feed";
    public static String ACTIVITY_ID = "Pet_feed";
    private static String ACTIVITY_URL = String.format("https://static.youstar.live/pet_feed/?activityId=%s", ACTIVITY_ID);


    // 喂养自己宠物100g获得爱心数
    private static final int FEED_SELF_PET_LOVE = 1;
    // 投球自己宠物获得爱心数
    private static final int THROW_BALL_SELF_PET_LOVE = 5;
    // 喂养Ta人宠物500自己获得爱心数
    private static final int FEED_OTHER_PET_LOVE = 1;
    // 投球Ta人宠物自己获得爱心数
    private static final int THROW_BALL_OTHER_PET_LOVE = 1;

    // 成为成年宠物的食量
    private static final int ADULT_PET_FOOD = 5000;
    // 年龄阶段每日需要的食物
    private static final List<Integer> AGE_STAGE_FOOD_LIST = Arrays.asList(1000, 2000);
    // 年龄阶段每日需要的玩具
    private static final List<Integer> AGE_STAGE_TOY_LIST = Arrays.asList(5, 10);

    // 每日喂食Ta人的宠物最大数量
    private static final int MAX_FEED_OTHER_PET = 500;
    // 每日投球Ta人宠物最大数量
    private static final int MAX_THROW_BALL_OTHER_PET = 1;

    // 签到奖励食物数量
    private static final List<Integer> SIGN_REWARD_FOOD_LIST = Arrays.asList(100, 100, 100, 150, 150, 150, 0);
    // 签到奖励玩具数量
    private static final List<Integer> SIGN_REWARD_TOY_LIST = Arrays.asList(0, 0, 0, 0, 0, 0, 1);

    private static final String CRASH_SHOP_DRAW_KEY = "CrashExchangeShop";

    private static final List<TaskConfigVO> DAILY_CONFIG_TASK_LIST = new ArrayList<>(); // 每日基础任务

    private static final List<TaskConfigVO> DAILY_CONFIG_ADVANCE_TASK_LIST = new ArrayList<>(); // 每日进阶任务

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final String eventExchangeTitle = "Crash Exchange Shop-exchange";

    private static final int HISTORY_USER_MAX_SIZE = 1000;
    private static final int HISTORY_PAGE_SIZE = 20;

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.ON_MIC_TIME_5_MINUTE,
            CommonMqTaskConstant.INVITE_BIND_USER);


    static {
        // 每日基础任务 currentProcess为每个单位获取的粮食数
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (1, 50, "share_snapchat", "", "", "", 0, "", "", "", ""));
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (1, 50, "on_mic_time_5_minute", "", "", "", 0, "", "", "", ""));
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (10, 100, "send_gift_500num", "", "", "", 0, "", "", "", ""));
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (10, 50, "receive_gift_500num", "", "", "", 0, "", "", "", ""));

        // 每日进阶任务 currentProcess为每个单位获取的玩具数
        DAILY_CONFIG_ADVANCE_TASK_LIST.add(new TaskConfigVO
                (5, 1, "recharge_110_diamond", "", "", "", 0, "", "", "", ""));

        DAILY_CONFIG_ADVANCE_TASK_LIST.add(new TaskConfigVO
                (5, 1, "invite_new_user", "", "", "", 0, "", "", "", ""));

        DAILY_CONFIG_ADVANCE_TASK_LIST.add(new TaskConfigVO
                (5, 1, "invite_old_user", "", "", "", 0, "", "", "", ""));

    }

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "ccc";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/pet_feed/?activityId=%s", ACTIVITY_ID);
        }

        logger.info("CrashExchangeShopService init");
    }


    // 个人锁-用户道具相关
    private String getLocalUserLockKey(String activityId, String uid) {
        return String.format("UserLockKey:%s:%s", activityId, uid);
    }

    // 宠物锁-宠物道具相关
    private String getLocalPetLockKey(String activityId, String uid) {
        return String.format("PetLockKey:%s:%s", activityId, uid);
    }

    // 活动期间个人总数据,filed为uid
    private String getHashUserKey(String activityId) {
        return String.format("pet_feed:user:%s", activityId);
    }

    // 活动期间宠物总数据,filed为uid
    private String getHashPetKey(String activityId) {
        return String.format("pet_feed:pet:%s", activityId);
    }


    // 个人每日任务完成情况,filed为uid
    private String getHashDayTaskKey(String activityId, String dateStr) {
        return String.format("pet_feed:day:task:%s:%s", activityId, dateStr);
    }

    // 每日已被喂养的宠物，set
    private String getSetDayFeedPetKey(String activityId, String dateStr) {
        return String.format("pet_feed:day:feed:%s:%s", activityId, dateStr);
    }


    // 个人的抽奖历史记录key
    private String getListHistoryKey(String activityId, String uid) {
        return String.format("pet_feed:history:%s:%s", activityId, uid);
    }

    public PetFeedVO crashExchangeShopConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivityNull(activityId);

        PetFeedVO vo = new PetFeedVO();

        return vo;
    }


    public void exchange(String activityId, String uid, String metaId) {
        if (!inActivityTime(activityId)) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        if (StringUtils.isEmpty(metaId)) {
            logger.info("metaId is empty. uid={} metaId={}", uid, metaId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        // 获取兑换商品配置
        ResourceKeyConfigData keyConfigData = resourceKeyHandlerService.getConfigData(CRASH_SHOP_DRAW_KEY);
        ResourceKeyConfigData.ResourceMeta targetMeta = null;

        if (keyConfigData != null && !CollectionUtils.isEmpty(keyConfigData.getResourceMetaList())) {
            for (ResourceKeyConfigData.ResourceMeta meta : keyConfigData.getResourceMetaList()) {
                if (metaId.equals(meta.getMetaId())) {
                    targetMeta = meta;
                    break;
                }
            }
        }

        if (targetMeta == null) {
            logger.info("targetMeta is null. uid={} metaId={}", uid, metaId);
            throw new CommonH5Exception(ActivityHttpCode.INCORRECT_INPUT_ID);
        }

        synchronized (stringPool.intern(getLocalUserLockKey(activityId, uid))) {

            int currentPoints = 0;
            int requiredPoints = Integer.parseInt(targetMeta.getRateNumber());

            if (currentPoints < requiredPoints) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "积分不足");
            }

            // 扣除积分


            // 发放奖励
            resourceKeyHandlerService.sendOneResourceData(uid, targetMeta, 905,
                    eventExchangeTitle, eventExchangeTitle, eventExchangeTitle, ACTIVITY_URL, "", 1);

            // 保存兑换历史
            PetFeedVO.HistoryRecordVO historyMeta = new PetFeedVO.HistoryRecordVO();

            historyMeta.setCtime(DateHelper.getNowSeconds());

            leftPushAllHistoryList(uid, Arrays.asList(historyMeta));

            doReportSpecialItemsEvent(ACTIVITY_ID, uid, 2, 2, requiredPoints);
        }
    }


    public PetFeedVO.HistoryRecordPageVO getHistoryListPageRecord(String activityId, String uid, int page) {
        PetFeedVO.HistoryRecordPageVO vo = new PetFeedVO.HistoryRecordPageVO();
        int start = (page - 1) * HISTORY_PAGE_SIZE;
        int end = page * HISTORY_PAGE_SIZE;
        String key = getListHistoryKey(activityId, uid);
        List<String> jsonList = activityCommonRedis.getCommonListPageRecord(key, start, end);
        List<PetFeedVO.HistoryRecordVO> resultList = new ArrayList<>();
        for (String json : jsonList) {
            PetFeedVO.HistoryRecordVO rewardData = JSON.parseObject(json, PetFeedVO.HistoryRecordVO.class);
            resultList.add(rewardData);
        }
        vo.setHistoryRecordList(resultList);
        vo.setNextPage(resultList.size() < HISTORY_PAGE_SIZE ? -1 : page + 1);
        return vo;
    }

    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();


        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return;
        }

        if (!checkAc(uid)) {
            return;
        }
        syncAddPoint(uid, data);
    }

    private void syncAddPoint(String uid, CommonMqTopicData data) {

    }

    private boolean checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }

    // 保存历史记录
    private void leftPushAllHistoryList(String uid, List<PetFeedVO.HistoryRecordVO> srcList) {
        String historyKey = getListHistoryKey(ACTIVITY_ID, uid);
        List<String> strList = new ArrayList<>();
        for (PetFeedVO.HistoryRecordVO meta : srcList) {
            String json = JSONObject.toJSONString(meta);
            strList.add(json);
        }
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(historyKey, strList, HISTORY_USER_MAX_SIZE);
    }


    // 下发榜单奖励
    public void distributionRanking(String activityId) {
        try {
            // 个人数据写mysql做持久化
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String aid, String resKey, String eventTitle) {
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }

    /**
     * 上报机会获取消耗事件
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int source, int num) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }


}
