package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.*;
import com.quhong.mysql.dao.RoomEventDao;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.redis.BackUserStateRedis;
import com.quhong.redis.RoomRocketV2Redis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CacheDataService {
    private static final Logger logger = LoggerFactory.getLogger(CacheDataService.class);
    @Resource
    protected ActivityCommonRedis activityCommonRedis;
    @Resource
    private RoomRocketV2Redis roomRocketV2Redis;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private BackUserStateRedis backUserStateRedis;


    @Cacheable(value = "activityCacheData", key = "#p0 + #p1",
            cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE)
    public Set<String> getAllSetCache(String acId, int type) {
        return type == 1 ? activityCommonRedis.getCommonHashAllKeyStr(acId)
                : activityCommonRedis.getCommonSetMember(acId);
    }

    @CachePut(value = "activityCacheData", key = "#p0 + #p1",
            cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE)
    public Set<String> putSetCache(String acId, int type, Set<String> value) {
        return value;
    }

    @CacheEvict(value = "activityCacheData", key = "#p0 + #p1",
            cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE)
    public void delSetCache(String acId, int type) {
    }


    @Cacheable(value = "activityCacheData", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#p0 + #p1")
    public String getCommonHashStrValue(String acId, String hashKey) {
        return activityCommonRedis.getCommonHashStrValue(acId, hashKey);
    }

    @CacheEvict(value = "activityCacheData", key = "#p0 + #p1",
            cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public void delCommonHashStrValue(String acId, String hashKey) {
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public LlluminateYouStarVO.DetailVO getLlluminateDetailVO(String detailKey, String roomId) {
        LlluminateYouStarVO.DetailVO detailVO;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(detailKey, roomId);
        if (StringUtils.hasLength(jsonValue)) {
            detailVO = JSONObject.parseObject(jsonValue, LlluminateYouStarVO.DetailVO.class);
            if (detailVO.getMicUsers() == null) {
                detailVO.setMicUsers(new HashSet<>());
            }
            if (detailVO.getSubUsers() == null) {
                detailVO.setSubUsers(new HashSet<>());
            }

        } else {
            detailVO = new LlluminateYouStarVO.DetailVO();
            detailVO.setDayStr(DateHelper.ARABIAN.formatDateInDay());
            detailVO.setDayPoint(0);
            detailVO.setSendGift(0L);
            detailVO.setNewUserMic(0);
            detailVO.setOldUserMic(0);
            detailVO.setNewUserSubNum(0);
            detailVO.setOldUserSubNum(0);
            detailVO.setMicUsers(new HashSet<>());
            detailVO.setSubUsers(new HashSet<>());
        }
        return detailVO;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Map<String, String> getFlagConfigMap() {
        List<ActivityCommonConfig.CommonFlagConfig> commonFlagConfigList = activityCommonConfig.getFlagConfigList();
        Map<String, String> commonFlagConfigMap = commonFlagConfigList.stream().collect
                (Collectors.toMap(ActivityCommonConfig.CommonFlagConfig::getCountryName,
                        ActivityCommonConfig.CommonFlagConfig::getFlagUrl));
        return commonFlagConfigMap;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public String isRunRoomEvent(String roomId) {
        int nowTime = DateHelper.getNowSeconds();
        RoomEventData ongoingRoomEvent = roomEventDao.getOngoingRoomEvent(roomId, nowTime);
        if (ongoingRoomEvent == null) {
            // 没有房间活动
            return null;
        } else {
            return String.valueOf(ongoingRoomEvent.getId());
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public RoomEventData roomEventByEventId(Integer eventId) {
        RoomEventData ongoingRoomEvent = roomEventDao.selectById(eventId);
        return ongoingRoomEvent;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public CarromGameVO.RedisDetail getCarromGameVORedisDetail(String detailKey, String uid) {
        CarromGameVO.RedisDetail detailVO;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(detailKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            detailVO = JSONObject.parseObject(jsonValue, CarromGameVO.RedisDetail.class);
            if (detailVO.getDayMapPlayCount() == null) {
                detailVO.setDayMapPlayCount(new HashMap<>());
            }
            if (detailVO.getDayMapCollect() == null) {
                detailVO.setDayMapCollect(new HashMap<>());
            }
        } else {
            detailVO = new CarromGameVO.RedisDetail();
            detailVO.setTotalStatus(0);
            detailVO.setDayMapPlayCount(new HashMap<>());
            detailVO.setDayMapCollect(new HashMap<>());
        }
        return detailVO;
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public RoomReturnBonusVO.DetailVO getRoomReturnBonusDetailVO(String detailKey, String roomId) {
        RoomReturnBonusVO.DetailVO detailVO;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(detailKey, roomId);
        if (StringUtils.hasLength(jsonValue)) {
            detailVO = JSONObject.parseObject(jsonValue, RoomReturnBonusVO.DetailVO.class);
            if (detailVO.getMicUsers() == null) {
                detailVO.setMicUsers(new HashSet<>());
            }
        } else {
            detailVO = new RoomReturnBonusVO.DetailVO();
            detailVO.setDayStr(DateHelper.ARABIAN.formatDateInDay());
            detailVO.setDayPoint(0);
            detailVO.setSendGiftPoint(0);
            detailVO.setUserMicPoint(0);
            detailVO.setMemberPoint(0);
            detailVO.setNewUserMic(0);
            detailVO.setOldUserMic(0);
            detailVO.setNewMemberNum(0);
            detailVO.setOldMemberNum(0);
            detailVO.setMicUsers(new HashSet<>());
        }
        return detailVO;
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public RoomReturnBonusVO.DetailVO getRocketV2Detail(String detailKey, String roomId) {
        RoomReturnBonusVO.DetailVO detailVO;
        String jsonValue = roomRocketV2Redis.getCommonHashStrValue(detailKey, roomId);
        if (StringUtils.hasLength(jsonValue)) {
            detailVO = JSONObject.parseObject(jsonValue, RoomReturnBonusVO.DetailVO.class);
            if (detailVO.getMicUsers() == null) {
                detailVO.setMicUsers(new HashSet<>());
            }
        } else {
            detailVO = new RoomReturnBonusVO.DetailVO();
            detailVO.setDayStr(DateHelper.ARABIAN.formatDateInDay());
            detailVO.setDayPoint(0);
            detailVO.setSendGiftPoint(0);
            detailVO.setUserMicPoint(0);
            detailVO.setMemberPoint(0);
            detailVO.setNewUserMic(0);
            detailVO.setOldUserMic(0);
            detailVO.setNewMemberNum(0);
            detailVO.setOldMemberNum(0);
            detailVO.setMicUsers(new HashSet<>());
        }
        return detailVO;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public SuperQueen2025VO.DetailInfo getSuperQueen2025VODetailInfo(String detailKey, String uid) {
        SuperQueen2025VO.DetailInfo detailInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(detailKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            detailInfo = JSONObject.parseObject(jsonValue, SuperQueen2025VO.DetailInfo.class);
            if (detailInfo.getGenerousGift27DUsers() == null) {
                detailInfo.setGenerousGift27DUsers(new HashSet<>());
            }
            if (detailInfo.getGenerousGift39DUsers() == null) {
                detailInfo.setGenerousGift39DUsers(new HashSet<>());
            }
            if (detailInfo.getGenerousGift49DUsers() == null) {
                detailInfo.setGenerousGift49DUsers(new HashSet<>());
            }
        } else {
            detailInfo = new SuperQueen2025VO.DetailInfo();
            detailInfo.setDayStr(DateHelper.ARABIAN.formatDateInDay());
            detailInfo.setPassionMicUsers(new HashSet<>());
            detailInfo.setPassionMomentCommentUsers(new HashSet<>());
            detailInfo.setPassionMomentLikeUsers(new HashSet<>());
            detailInfo.setPassionFriendsUsers(new HashSet<>());
            detailInfo.setPassionPrivateGiftUsers(new HashSet<>());
            detailInfo.setGenerousGift27DUsers(new HashSet<>());
            detailInfo.setGenerousGift39DUsers(new HashSet<>());
            detailInfo.setGenerousGift49DUsers(new HashSet<>());
        }
        return detailInfo;
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public SuperQueen2025VO.MyQueenInfo getSuperQueen2025VOMyQueenInfo(String totalInfoKey, String uid) {
        SuperQueen2025VO.MyQueenInfo myQueenInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(totalInfoKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            myQueenInfo = JSONObject.parseObject(jsonValue, SuperQueen2025VO.MyQueenInfo.class);
        } else {
            myQueenInfo = new SuperQueen2025VO.MyQueenInfo();
            myQueenInfo.setCharmAgainSw(1);
            myQueenInfo.setGenerousAgainSw(1);
            myQueenInfo.setPassionAgainSw(1);
            myQueenInfo.setCharmAgainSet(new HashSet<>());
            myQueenInfo.setGenerousAgainSet(new HashSet<>());
            myQueenInfo.setPassionAgainSet(new HashSet<>());
            myQueenInfo.setCharmLevelMapCollect(new HashMap<>());
            myQueenInfo.setGenerousLevelMapCollect(new HashMap<>());
            myQueenInfo.setPassionLevelMapCollect(new HashMap<>());
            myQueenInfo.setAdminRoomSet(new HashSet<>());
        }
        return myQueenInfo;
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public GameEventOrganizerVO.DetailInfo getGameEventOrganizerVOInfo(String detailKey, String dayStr, String roomId) {
        GameEventOrganizerVO.DetailInfo detailInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(detailKey, roomId);
        if (StringUtils.hasLength(jsonValue)) {
            detailInfo = JSONObject.parseObject(jsonValue, GameEventOrganizerVO.DetailInfo.class);
        } else {
            detailInfo = new GameEventOrganizerVO.DetailInfo();
            detailInfo.setDayStr(dayStr);
            detailInfo.setCtime(DateHelper.getNowSeconds());
            detailInfo.setMicDevices(new HashSet<>());
            detailInfo.setSubDevices(new HashSet<>());
        }
        return detailInfo;
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public GameEventOrganizerVO.ChallengeInfo getGameEventOrganizerVOChallengeInfo(String totalInfoKey, String roomId) {
        GameEventOrganizerVO.ChallengeInfo challengeInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(totalInfoKey, roomId);
        if (StringUtils.hasLength(jsonValue)) {
            challengeInfo = JSONObject.parseObject(jsonValue, GameEventOrganizerVO.ChallengeInfo.class);
        } else {
            challengeInfo = new GameEventOrganizerVO.ChallengeInfo();
            challengeInfo.setLevelMapCollect(new HashMap<>());
            challengeInfo.setLevelMapAidList(new HashMap<>());
        }
        return challengeInfo;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public GameEventOrganizerVO.MyGiftDetailInfo getGameEventOrganizerVOMyGiftDetailInfo(String detailKey, String dayStr, String uid) {
        GameEventOrganizerVO.MyGiftDetailInfo myGiftDetailInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(detailKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            myGiftDetailInfo = JSONObject.parseObject(jsonValue, GameEventOrganizerVO.MyGiftDetailInfo.class);
        } else {
            myGiftDetailInfo = new GameEventOrganizerVO.MyGiftDetailInfo();
            myGiftDetailInfo.setDayStr(dayStr);
            myGiftDetailInfo.setSubEventIds(new HashSet<>());
            myGiftDetailInfo.setMicEventIds(new HashSet<>());
        }
        return myGiftDetailInfo;
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public CarromMasterVO.TotalInfo getCarromMasterVOTotalInfo(String totalInfoKey, String uid) {
        CarromMasterVO.TotalInfo totalInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(totalInfoKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            totalInfo = JSONObject.parseObject(jsonValue, CarromMasterVO.TotalInfo.class);
        } else {
            totalInfo = new CarromMasterVO.TotalInfo();
            totalInfo.setDaySet(new HashSet<>());

        }
        return totalInfo;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public CarromMasterVO.DetailInfo getCarromMasterVODetailInfo(String detailKey, String dayStr, String uid) {
        CarromMasterVO.DetailInfo detailInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(detailKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            detailInfo = JSONObject.parseObject(jsonValue, CarromMasterVO.DetailInfo.class);
        } else {
            detailInfo = new CarromMasterVO.DetailInfo();
            detailInfo.setDayStr(dayStr);
        }
        return detailInfo;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public GameCarnivalVO.TotalInfo getGameCarnivalVOTotalInfo(String totalInfoKey, String uid) {
        GameCarnivalVO.TotalInfo totalInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(totalInfoKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            totalInfo = JSONObject.parseObject(jsonValue, GameCarnivalVO.TotalInfo.class);
        } else {
            totalInfo = new GameCarnivalVO.TotalInfo();
            totalInfo.setDaySet(new HashSet<>());

        }
        return totalInfo;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public GameCarnivalVO.DetailInfo getGameCarnivalVODetailInfo(String detailKey, String dayStr, String uid) {
        GameCarnivalVO.DetailInfo detailInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(detailKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            detailInfo = JSONObject.parseObject(jsonValue, GameCarnivalVO.DetailInfo.class);
            if(detailInfo.getWinGameType()==null){
                detailInfo.setWinGameType(new HashSet<>());
            }
        } else {
            detailInfo = new GameCarnivalVO.DetailInfo();
            detailInfo.setWinGameType(new HashSet<>());
            detailInfo.setDayStr(dayStr);
        }
        return detailInfo;
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public RoomDataCenterVO.DetailInfo getRoomDataCenterVODetailInfo(String detailKey, String dayStr, String roomId) {
        RoomDataCenterVO.DetailInfo detailInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(detailKey, roomId);
        if (StringUtils.hasLength(jsonValue)) {
            detailInfo = JSONObject.parseObject(jsonValue, RoomDataCenterVO.DetailInfo.class);
            if (detailInfo.getInviteUserSet() == null) {
                detailInfo.setInviteUserSet(new HashSet<>());
            }
            if (detailInfo.getInviteNewUserSet() == null) {
                detailInfo.setInviteNewUserSet(new HashSet<>());
            }
            if (detailInfo.getAllUserMicSet() == null) {
                detailInfo.setAllUserMicSet(new HashSet<>());
            }
            if (detailInfo.getNewUserMicSet() == null) {
                detailInfo.setNewUserMicSet(new HashSet<>());
            }
            if (detailInfo.getSendGiftSet() == null) {
                detailInfo.setSendGiftSet(new HashSet<>());
            }
            if (detailInfo.getSendGiftEventSet() == null) {
                detailInfo.setSendGiftEventSet(new HashSet<>());
            }
            if (detailInfo.getAllUserMicEventSet() == null) {
                detailInfo.setAllUserMicEventSet(new HashSet<>());
            }
            if (detailInfo.getNewUserMicEventSet() == null) {
                detailInfo.setNewUserMicEventSet(new HashSet<>());
            }
        } else {
            detailInfo = new RoomDataCenterVO.DetailInfo();
            detailInfo.setDayStr(dayStr);
            detailInfo.setInviteUserSet(new HashSet<>());
            detailInfo.setInviteNewUserSet(new HashSet<>());
            detailInfo.setAllUserMicSet(new HashSet<>());
            detailInfo.setNewUserMicSet(new HashSet<>());
            detailInfo.setSendGiftSet(new HashSet<>());
            detailInfo.setSendGiftEventSet(new HashSet<>());
            detailInfo.setAllUserMicEventSet(new HashSet<>());
            detailInfo.setNewUserMicEventSet(new HashSet<>());
        }
        return detailInfo;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public ArabicPainterVO.ArabicPainterInfo getArabicPainterInfo(String totalInfoKey, String uid) {
        ArabicPainterVO.ArabicPainterInfo arabicPainterInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(totalInfoKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            arabicPainterInfo = JSONObject.parseObject(jsonValue, ArabicPainterVO.ArabicPainterInfo.class);
        } else {
            arabicPainterInfo = new ArabicPainterVO.ArabicPainterInfo();
            arabicPainterInfo.setDayMapLikeState(new HashMap<>());
            arabicPainterInfo.setDayMapMomentId(new HashMap<>());
            arabicPainterInfo.setDayMapCommentAidList(new HashMap<>());
        }
        return arabicPainterInfo;
    }

    /**
     * 获取用户首充充值优惠信息，带缓存
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public LimitedRechargeVO.LimitedRechargeInfo getLimitedRechargeInfoCache(String limitedRechargeKey, String uid) {
        LimitedRechargeVO.LimitedRechargeInfo info;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(limitedRechargeKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            info = JSONObject.parseObject(jsonValue, LimitedRechargeVO.LimitedRechargeInfo.class);
        } else {
            info = new LimitedRechargeVO.LimitedRechargeInfo();
        }
        return info;
    }

    /**
     * 清除用户首充充值优惠信息缓存
     */
    @CacheEvict(value = "cache",
            key = "'getLimitedRechargeInfo'+T(org.springframework.util.StringUtils).arrayToDelimitedString({#limitedRechargeKey, #uid},'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE)
    public void delLimitedRechargeInfo(String limitedRechargeKey, String uid) {
    }

    /**
     * 获取用户推送状态，带缓存
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public LimitedRechargeVO.PushStatus getPushStatusCache(String pushStatusKey, String uid) {
        LimitedRechargeVO.PushStatus pushStatus;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(pushStatusKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            pushStatus = JSONObject.parseObject(jsonValue, LimitedRechargeVO.PushStatus.class);
        } else {
            pushStatus = new LimitedRechargeVO.PushStatus();
        }
        return pushStatus;
    }

    /**
     * 清除用户推送状态缓存
     */
    @CacheEvict(value = "cache",
            key = "'getPushStatusCache'+T(org.springframework.util.StringUtils).arrayToDelimitedString({#pushStatusKey, #uid},'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE)
    public void delPushStatusCache(String pushStatusKey, String uid) {
    }

    /**
     * 获取推送字段值，带缓存
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public String getPushFieldCache(String pushStatusKey, String field) {
        String value = activityCommonRedis.getCommonHashStrValue(pushStatusKey, field);
        return value != null ? value : "0";
    }

    /**
     * 清除推送字段缓存
     */
    @CacheEvict(value = "cache",
            key = "'getPushFieldCache'+T(org.springframework.util.StringUtils).arrayToDelimitedString({#pushStatusKey, #field},'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE)
    public void delPushFieldCache(String pushStatusKey, String field) {
    }

//    @CachePut(value = "rocketRewardCacheData", key = "#p0 + #p1+#p2 + #p3",
//            cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
//    public List<RocketRewardConfigData.ResourceMeta> putRocketRewardCache(String day, String roomId, String aid, int rocketLevel,
//                                                                          List<RocketRewardConfigData.ResourceMeta> value) {
//        return value;
//    }
//
//    @Cacheable(value = "rocketRewardCacheData", key = "#p0 + #p1+#p2 + #p3",
//            cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
//    public List<RocketRewardConfigData.ResourceMeta> getRocketRewardCache(String day, String roomId, String aid, int rocketLevel,
//                                                                          List<RocketRewardConfigData.ResourceMeta> value) {
//        return value;
//    }
}
