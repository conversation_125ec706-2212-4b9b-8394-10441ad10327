package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.vo.AnniversaryV7VO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AnniversaryV7Service extends OtherActivityService implements DailyTaskHandler {

    private static final Logger logger = LoggerFactory.getLogger(AnniversaryV7Service.class);
    private static final String ACTIVITY_TITLE_EN = "7st Anniversary Big Benefit";
    private static final String ACTIVITY_TITLE_SIGN_EN = "7st Anniversary Big Benefit Re Sign";
    private static final String ACTIVITY_DESC = "7st Anniversary Big Benefit Reward";
    private static final String ACTIVITY_TITLE_AR = "للذكرى السابعة  فوائد كبيرة";
    private static final String ACTIVITY_ID = "66e55fc4750550deab932ddf";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live//benefit_7th/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv//benefit_7th/?activityId=%s", ACTIVITY_ID);
    private static final List<String> SIGN_DATE_LIST = Arrays.asList("2024-09-18", "2024-09-19", "2024-09-20", "2024-09-21", "2024-09-22", "2024-09-23", "2024-09-24");
    private static final List<String> SIGN_KEY_LIST = Arrays.asList("anniversaryV7Sign1", "anniversaryV7Sign2", "anniversaryV7Sign3",
            "anniversaryV7Sign4", "anniversaryV7Sign5", "anniversaryV7Sign6", "anniversaryV7Sign7");
    private static final String SIGN_KEY_FORMAT = "anniversaryV7Sign%s";
    private static final String LUCKY_KEY_FORMAT = "anniversaryV7Day%sLucky%s";
    private static final Integer LUCKY_USER_TYPE_NUM = 3;
    private static final List<Integer> LUCKY_USER_TYPE_LIST = Arrays.asList(1, 2, 3, 4);
    private static final String SIGN_ON_MIC_TIME = "on_mic_time";
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final String MOMENT_TEXT_EN = "Happy 7st birthday to YouStar, come and join the big day to get free rewards.";
    private static final String MOMENT_TEXT_AR = "عيد ميلاد سعيد السابع لـ YouStar، تعال وانضم إلى اليوم الكبير للحصول على مكافآت مجانية.";

    @Resource
    private EventReport eventReport;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private IMomentService iMomentService;
    @Resource
    private OfficialMsgService officialMsgService;
    @Resource
    private WhiteTestDao whiteTestDao;

    private String getHashActivityId(String activityId, String uid) {
        return String.format("anniversaryV1:%s:%s", activityId, uid);
    }

    private String getDailyUserHashActivityId(String activityId, String uid, String dateStr) {
        return String.format("anniversaryV1UserDaily:%s:%s:%s", activityId, uid, dateStr);
    }

    private String getDailyHashActivityId(String activityId, String dateStr) {
        return String.format("anniversaryV1Daily:%s:%s", activityId, dateStr);
    }

    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }

    /**
     * 每日签到设备去重
     */
    private String getSignTnSetKey(String activityId, String dateStr) {
        return String.format("dailySignTn:%s:%s", activityId, dateStr);
    }

    /**
     * 每日参与幸运儿集合
     */
    private String getLuckySetKey(String activityId, String dateStr) {
        return String.format("dailyLucky:%s:%s", activityId, dateStr);
    }

    private String getLuckyTnSetKey(String activityId, String dateStr) {
        return String.format("dailyTnLucky:%s:%s", activityId, dateStr);
    }


    /**
     * 每日参与用户分群集合
     */
    private String getLuckyClassifySetKey(String activityId, String dateStr, int userType) {
        return String.format("dailyLuckyClassify:%s:%s:%s", activityId, dateStr, userType);
    }

    /**
     * 已经获得幸运儿的集合
     */
    private String getLuckyAlreadySetKey(String activityId) {
        return String.format("dailyLuckyAlready:%s", activityId);
    }

    private Map<String, ResourceKeyConfigData> getResourceDataMap() {
        List<ResourceKeyConfigData> resourceKeyConfigDataList = resourceKeyConfigDao.findListByKeys(new HashSet<>(SIGN_KEY_LIST));
        return resourceKeyConfigDataList.stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));
    }

    public AnniversaryV7VO anniversaryV1Config(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        AnniversaryV7VO vo = new AnniversaryV7VO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();

        // String dateStr = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        LocalDate currentDate = LocalDate.parse(dateStr, formatter);
        vo.setCurrentDate(dateStr);

        // 用户活动数据
        String hashActivityId = this.getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);


        // 用户每日数据
        String dailyUserHashActivityId = this.getDailyUserHashActivityId(activityId, uid, dateStr);
        Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(dailyUserHashActivityId);
        int userOnMicTime  = userDailyDataMap.getOrDefault(SIGN_ON_MIC_TIME, 0);
        vo.setFirstEntry(userDailyDataMap.getOrDefault("firstEntry", 1));
        if(vo.getFirstEntry() == 1){
            activityCommonRedis.setCommonHashNum(dailyUserHashActivityId, "firstEntry", 0);
        }

        Map<String, ResourceKeyConfigData> resourceKeyConfigDataMap = this.getResourceDataMap();
        List<AnniversaryV7VO.SignInfo> signInfoList = new ArrayList<>();
        List<AnniversaryV7VO.DailyLuckyConfig> dailyLuckyConfigList = new ArrayList<>();
        // 签到配置相关
        for (int i = 1; i <= SIGN_DATE_LIST.size(); i++) {
            AnniversaryV7VO.SignInfo signInfo = new AnniversaryV7VO.SignInfo();
            String signDate = SIGN_DATE_LIST.get(i - 1);
            signInfo.setSignDate(signDate);
            String resKey = String.format(SIGN_KEY_FORMAT, i);
            signInfo.setResourceKeyConfigData(resourceKeyConfigDataMap.get(resKey));

            // 设置签到状态
            int curDateTnStatus = activityCommonRedis.isCommonSetData(this.getSignTnSetKey(activityId, signDate), tnId);
            LocalDate signDateFormat = LocalDate.parse(signDate, formatter);
            int signStatus = userDataMap.getOrDefault(signDate, 0);
            int signFlag = 0;
            if (currentDate.isBefore(signDateFormat)) {   // 未到日期不可签到
                signFlag = 0;
            } else if (currentDate.isAfter(signDateFormat)) {  //
                if (signStatus > 0) {
                    signFlag = 2;
                }
                if (signStatus <= 0 && userOnMicTime > 10 && curDateTnStatus <= 0) {
                    signFlag = 1;
                }
            } else {
                if (signStatus <= 0) {
                    synchronized (stringPool.intern("sign" + uid)) {
                        if(curDateTnStatus > 0){
                            vo.setDeviceSign(1);
                            signFlag = 0;
                        }else {
                            signFlag = 2;
                            activityCommonRedis.setCommonHashNum(hashActivityId, signDate, 1);
                            activityCommonRedis.addCommonSetData(this.getSignTnSetKey(activityId, signDate), tnId);
                            resourceKeyHandlerService.sendResourceData(uid, resKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, "");
                        }
                    }
                }else {
                    signFlag = 2;
                }
            }
            signInfo.setSignStatus(signFlag);
            signInfoList.add(signInfo);

            // 参与每日幸运儿相关
            AnniversaryV7VO.DailyLuckyConfig dailyLuckyConfig = new AnniversaryV7VO.DailyLuckyConfig();
            dailyLuckyConfig.setDateStr(signDate);
            dailyLuckyConfig.setLuckyNumber(activityCommonRedis.getCommonSetNum(getLuckySetKey(activityId, signDate)));
            dailyLuckyConfig.setLuckyStatus(activityCommonRedis.isCommonSetData(getLuckySetKey(activityId, signDate), uid));
            dailyLuckyConfig.setLuckyTnStatus(activityCommonRedis.isCommonSetData(getLuckyTnSetKey(activityId, signDate), tnId));

            List<AnniversaryV7VO.LuckyConfig> luckyConfigList = new ArrayList<>();
            String signDateHashActivityId = this.getDailyHashActivityId(activityId, signDate);
            Map<String, String> signDateDataMap = activityCommonRedis.getCommonHashAllMapStr(signDateHashActivityId);
            for (int j = 1; j <= LUCKY_USER_TYPE_NUM; j++) {
                AnniversaryV7VO.LuckyConfig luckyConfig = new AnniversaryV7VO.LuckyConfig();
                String luckyResKey = String.format(LUCKY_KEY_FORMAT, i, j);
                luckyConfig.setResKey(luckyResKey);
                String luckyUserUid = signDateDataMap.get(luckyResKey);
                if (!ObjectUtils.isEmpty(luckyUserUid)) {
                    ActorData luckyActorData = actorDao.getActorDataFromCache(luckyUserUid);
                    luckyConfig.setUid(luckyActorData.getUid());
                    luckyConfig.setName(luckyActorData.getName());
                    luckyConfig.setHead(ImageUrlGenerator.generateRoomUserUrl(luckyActorData.getHead()));
                }
                luckyConfigList.add(luckyConfig);

            }
            dailyLuckyConfig.setLuckyConfigList(luckyConfigList);
            dailyLuckyConfigList.add(dailyLuckyConfig);
        }
        vo.setSignInfoList(signInfoList);
        vo.setDailyLuckyConfigList(dailyLuckyConfigList);
        return vo;
    }

    public void anniversaryV1Sign(String activityId, String uid, String signDate) {

        checkActivityTime(activityId);
        if (!SIGN_DATE_LIST.contains(signDate)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();

        String hashActivityId = this.getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

        int signStatus = userDataMap.getOrDefault(signDate, 0);
        if (signStatus > 0) {
            throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
        }

        // String dateStr = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        String dateStr = DateHelper.ARABIAN.formatDateInDay();

        if(activityCommonRedis.isCommonSetData(this.getSignTnSetKey(activityId, signDate), tnId) > 0){
            logger.error("anniversaryV1Sign deviceLimit uid:{}, tn_id:{}", uid, tnId);
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        LocalDate currentDate = LocalDate.parse(dateStr, formatter);
        LocalDate signDateFormat = LocalDate.parse(signDate, formatter);
        if (!signDateFormat.isBefore(currentDate)) {
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        String dailyHashActivityId = this.getDailyUserHashActivityId(activityId, uid, dateStr);
        Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
        int userOnMicTime = userDailyDataMap.getOrDefault(SIGN_ON_MIC_TIME, 0);
        if (userOnMicTime <= 10) {
            logger.error("anniversaryV1Sign userOnMicTime low uid:{}, tn_id:{} userOnMicTime:{}", uid, tnId, userOnMicTime);
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        synchronized (stringPool.intern("sign" + uid)) {
            activityCommonRedis.setCommonHashNum(hashActivityId, signDate, 1);
            activityCommonRedis.addCommonSetData(this.getSignTnSetKey(activityId, signDate), tnId);
            int index = SIGN_DATE_LIST.indexOf(signDate);
            String resKey = String.format(SIGN_KEY_FORMAT, index + 1);
            resourceKeyHandlerService.sendResourceData(uid, resKey, ACTIVITY_TITLE_SIGN_EN, ACTIVITY_TITLE_AR, ACTIVITY_TITLE_SIGN_EN, ACTIVITY_URL, "");
        }
    }

    private int getLuckyUserType(String uid) {
        int endTime = DateHelper.getNowSeconds();
        int startTime = endTime - 30 * 86400;
        int rechargeMoney = (int) rechargeDailyInfoDao.selectUserRechargeAmount(uid, startTime);
        if (rechargeMoney >= 1000) {
            return 1;
        } else if (rechargeMoney >= 300) {
            return 2;
        } else if (rechargeMoney >= 1) {
            return 3;
        }
        return 4;
    }


    public void anniversaryV1Join(String activityId, String uid) {
        checkActivityTime(activityId);
        // String dateStr = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();
        if(activityCommonRedis.isCommonSetData(getLuckyTnSetKey(activityId, dateStr), tnId) > 0){
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        activityCommonRedis.addCommonSetData(getLuckySetKey(activityId, dateStr), uid);
        if (activityCommonRedis.isCommonSetData(getLuckyAlreadySetKey(activityId), uid) <= 0) {
            int userType = this.getLuckyUserType(uid);
            activityCommonRedis.addCommonSetData(getLuckyClassifySetKey(activityId, dateStr, userType), uid);
            activityCommonRedis.addCommonSetData(getLuckyTnSetKey(activityId, dateStr), tnId);
            this.doJoinReportEvent(uid);
        }
    }

    private void doJoinReportEvent(String uid) {
        ActivityApplicationEvent event = new ActivityApplicationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScene(ACTIVITY_TITLE_EN);
        eventReport.track(new EventDTO(event));
    }

    // public void anniversaryV1SetDate(String activityId, String signDate) {
    //     LocalDate localDate = DateSupport.parse(signDate);
    //     LocalDate lastLocalDate = localDate.plusDays(-1);
    //     String lastDay = DateSupport.format(lastLocalDate);
    //     dailyTaskRun(lastDay);
    //     activityCommonRedis.setCommonStrData(this.getDailyDate(activityId), signDate);
    // }


    private String getLuckyUserOrderUserType(String activityId, String dateStr, int userType, Set<String> luckyAlreadySet) {
        Set<String> luckyUserTypeUidSet = activityCommonRedis.getCommonSetMember(this.getLuckyClassifySetKey(activityId, dateStr, userType));
        List<String> luckyUserTypeUidList = new ArrayList<>(luckyUserTypeUidSet);
        Collections.shuffle(luckyUserTypeUidList);

        for (String luckyUserUid: luckyUserTypeUidList) {
            if(!ObjectUtils.isEmpty(luckyUserUid) && !luckyAlreadySet.contains(luckyUserUid)){
                return luckyUserUid;
            }
        }

        String otherLuckyUid = null;
        for (Integer type : LUCKY_USER_TYPE_LIST) {
            if(type == userType){
                continue;
            }

            Set<String> luckyUserUidSet = activityCommonRedis.getCommonSetMember(this.getLuckyClassifySetKey(activityId, dateStr, type));
            List<String> luckyUidList = new ArrayList<>(luckyUserUidSet);
            Collections.shuffle(luckyUidList);

            for (String luckyUserUid: luckyUidList) {
                if(!luckyAlreadySet.contains(luckyUserUid)){
                    otherLuckyUid = luckyUserUid;
                    break;
                }
            }

            if(!ObjectUtils.isEmpty(otherLuckyUid)){
                return otherLuckyUid;
            }
        }
        return otherLuckyUid;
    }


    public void anniversaryV1ShareMoment(String uid, String activityId, int slang) {
        synchronized (stringPool.intern(uid)) {

            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String dailyUserHashActivityId = this.getDailyUserHashActivityId(activityId, uid, dateStr);
            Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(dailyUserHashActivityId);
            int shareMoment = userDailyDataMap.getOrDefault("shareMoment", 0);
            if(shareMoment > 0){
                throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE);
            }


            // 发布朋友圈
            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();
            publishMomentDTO.setUid(uid);
            String momentText = slang == SLangType.ARABIC ? MOMENT_TEXT_AR : MOMENT_TEXT_EN;
            publishMomentDTO.setText(momentText);
            publishMomentDTO.setShow(1);
            publishMomentDTO.setActiveId(activityId);
            publishMomentDTO.setLocation(ACTIVITY_TITLE_EN);
            InnerPublishMomentDTO.Quote quote = new InnerPublishMomentDTO.Quote();
            quote.setAction(ACTIVITY_URL);
            quote.setIcon("https://cdn3.qmovies.tv/youstar/op_1726299673_fjrk.png");
            quote.setContent(momentText);
            quote.setType(6);
            publishMomentDTO.setQuote(quote);
            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if (result.getCode() == 20) {
                logger.info("newYearExpect level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.getCode() == 41) {
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
            }

            if (result.isError()) {
                logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            activityCommonRedis.incCommonHashNum(dailyUserHashActivityId, "shareMoment", 1);
        }
    }

    public void handleMqMsg(CommonMqTopicData data) {
        String fromUid = data.getUid();
        if(!inActivityTime(ACTIVITY_ID)){
            return;
        }
        // if (!whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)){
        //     return;
        // }

        if(!CommonMqTaskConstant.ON_MIC_TIME.equals(data.getItem())){
            return;
        }

        synchronized (stringPool.intern(ACTIVITY_ID + "_" + fromUid)) {
            int value = data.getValue();
            // 增加积分值
            // String currentDate = activityCommonRedis.getCommonStrValue(this.getDailyDate(ACTIVITY_ID));
            String currentDate = DateHelper.ARABIAN.formatDateInDay();

            String dailyUserHashKey =  getDailyUserHashActivityId(ACTIVITY_ID, fromUid, currentDate);
            int totalNum = activityCommonRedis.getCommonHashValue(dailyUserHashKey, SIGN_ON_MIC_TIME);

            if(totalNum >= 20){
                return;
            }
            totalNum += value;
            activityCommonRedis.setCommonHashNum(dailyUserHashKey, SIGN_ON_MIC_TIME, totalNum);
        }
    }

    @Override
    public void dailyTaskRun(String dateStr) {

        try {
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
            if (activityData == null) {
                return;
            }
            int endTime = activityData.getEndTime();
            int currentTime = DateHelper.getNowSeconds();
            if (currentTime - 3600 > endTime) {
                return;
            }

            if (ObjectUtils.isEmpty(dateStr)) {
                dateStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
            }
            logger.info("dailyTaskRun anniversaryV1");
            if(!SIGN_DATE_LIST.contains(dateStr)){
                logger.error("dailyTaskRun anniversaryV1 SIGN_DATE_LIST not find dateStr:{}", dateStr);
                return;
            }

            int dayNum = SIGN_DATE_LIST.indexOf(dateStr) + 1;
            String luckyAlreadySetKey = this.getLuckyAlreadySetKey(ACTIVITY_ID);
            Set<String> luckyAlreadySet = activityCommonRedis.getCommonSetMember(luckyAlreadySetKey);

            for (int j = 1; j <= LUCKY_USER_TYPE_NUM; j++) {
                String luckyUid = this.getLuckyUserOrderUserType(ACTIVITY_ID, dateStr, j, luckyAlreadySet);
                logger.info("dailyTaskRun anniversaryV1 dayNum:{}, userType:{}, luckyUid:{}", dayNum, j, luckyUid);
                String luckyResKey = String.format(LUCKY_KEY_FORMAT, dayNum, j);
                if(!ObjectUtils.isEmpty(luckyUid)){
                    String dailyHashActivityId = this.getDailyHashActivityId(ACTIVITY_ID, dateStr);
                    activityCommonRedis.setCommonHashData(dailyHashActivityId, luckyResKey, luckyUid);
                    activityCommonRedis.addCommonSetData(luckyAlreadySetKey, luckyUid);
                    luckyAlreadySet.add(luckyUid);
                    this.sendOfficialData(luckyUid, luckyResKey, dateStr);
                    resourceKeyHandlerService.sendResourceData(luckyUid, luckyResKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, "");
                }
            }
        } catch (Exception e) {
            logger.error("distribution anniversaryV1 error: {}", e.getMessage(), e);
        }
    }

    private void sendOfficialData(String luckyUid, String resKey, String dateStr) {
        ActorData actorData = actorDao.getActorDataFromCache(luckyUid);
        int slang = actorData.getSlang();
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(luckyUid);
        officialData.setTitle(slang == SLangType.ENGLISH ? ACTIVITY_TITLE_EN : ACTIVITY_TITLE_AR);
        officialData.setBody(slang == SLangType.ENGLISH ? "Congratulations for being chosen to be the lucky person of YouStar 7 anniverssary you have gotten the rewards as below" : "تهانينا لاختيارك لتكون الشخص المحظوظ في الذكرى السابعةليوستار  لقد حصلت على المكافآت كما هو موضح أدناه");
        officialData.setValid(1);
        officialData.setNtype(1);
        officialData.setAtype(0);
        officialData.setNews_type(6);
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resKey);
        List<OfficialData.AwardInfo> awardList = new ArrayList<>();
        if(resourceKeyConfigData != null){
            for (ResourceKeyConfigData.ResourceMeta resourceMeta: resourceKeyConfigData.getResourceMetaList()) {
                OfficialData.AwardInfo awardInfo = new OfficialData.AwardInfo();
                awardInfo.setName(slang == SLangType.ENGLISH ? resourceMeta.getResourceNameEn() : resourceMeta.getResourceNameAr());
                awardInfo.setIcon(resourceMeta.getResourceIcon());
                awardList.add(awardInfo);

                LuckyUserEvent event = new LuckyUserEvent();
                event.setUid(luckyUid);
                event.setCtime(DateHelper.getNowSeconds());
                event.setDate(dateStr);
                event.setScene(ACTIVITY_TITLE_EN);
                event.setReward(resourceMeta.getResourceNameEn());
                eventReport.track(new EventDTO(event));
            }
        }
        officialData.setAward_list(awardList);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialMsgService.officialMsgPush(officialData);
    }
}
