package com.quhong.service;

import com.quhong.config.ActivityCommonConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.MqTopicItemConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.EnterRoomMqData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.AnniversarySixVO;
import com.quhong.data.vo.AnniversaryStarVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MomentActivityDao;
import com.quhong.mongo.data.MomentCountData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.ActivityMemoryDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.ActivityMemoryData;
import com.quhong.redis.AnniversaryRedis;
import com.quhong.room.TestRoomService;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;


/**
 * 记忆殿堂
 */
@Service
public class AnniversarySixService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(AnniversarySixService.class);
    private static final String ACTIVITY_TITLE_EN = "Memory Palace 2024";
    private static final String ACTIVITY_TITLE_AR = "قصر الذاكرة 2024";
    private static final String ACTIVITY_ID = "66ebd76ddf9fd18e6e732579";
    private static final String ACTIVITY_SHARE_MOMENT = "shareMoment";
    private static final String ACTIVITY_ORIGIN = "memory_palace";
    private static final String BACK_ACTIVITY_ID = "back_activity";
    private static final Integer COUNT_TIME_START = 1710450000;
    private static final Integer COUNT_TIME_END = 1727384400;
    private static final String LOYALTY = "loyalty";                       // 忠诚达人
    private static Integer LOYALTY_Id = null;                               // 忠诚达人
    private static final String ACTIVE = "active";                        // 活跃达人
    private static Integer ACTIVE_Id = null;
    private static final String SOCIAL = "social";                        // 社交达人
    private static Integer SOCIAL_Id = null;
    private static final String BEST_FRIEND = "bestFriend";                    // 最佳友人
    private static Integer BEST_FRIEND_Id = null;
    private static final String ACE_RED = "aceRed";                        // 王牌红人
    private static Integer ACE_RED_Id = null;

    // 点亮周年
    private static final String STAR_LISTEN = "listen";
    private static final Integer STAR_LISTEN_LIMIT = 6;
    private static final String STAR_TALK = "talk";
    private static final Integer STAR_TALK_LIMIT = 30;
    private static final String STAR_HAPPY = "happy";
    private static final Integer STAR_HAPPY_LIMIT = 3;
    private static final String STAR_GUARD = "guard";
    private static final String STAR_SUPPORT = "support";
    private static final String STAR_ACCOMPANY = "accompany";
    private static final String STAR_BAG_STATUS = "bagStatus";
    private static final Integer BAG_GIFT1 = 44;
    private static Integer BAG_GIFT2 = null;
    private static Integer BAG_ROOM_PICTURE = null;
    private static Integer BAG_FLOAT = null;
    private static final String ACTIVITY_ID_6TH = "650274f3e00149b38b097a74";
    private static final Integer STAR_BEAN_LIMIT = 66;


    static {
        if (ServerConfig.isProduct()) {
            LOYALTY_Id = 16;
            ACTIVE_Id = 17;
            SOCIAL_Id = 18;
            BEST_FRIEND_Id = 19;
            ACE_RED_Id = 20;

            BAG_GIFT2 = 601;
            BAG_ROOM_PICTURE = 295;
            BAG_FLOAT = 45;
        } else {
            LOYALTY_Id = 79;
            ACTIVE_Id = 80;
            SOCIAL_Id = 81;
            BEST_FRIEND_Id = 82;
            ACE_RED_Id = 83;

            BAG_GIFT2 = 742;
            BAG_ROOM_PICTURE = 245;
            BAG_FLOAT = 50;

        }
    }


    @Resource
    private ActorDao actorDao;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private ActivityMemoryDao activityMemoryDao;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private MomentActivityDao momentActivityDao;
    @Resource
    private AnniversarySixService anniversarySixService;
    @Resource
    private AnniversaryRedis anniversaryRedis;
    @Resource
    private WhiteTestDao whiteTestDao;

    @Cacheable(value = "getMomentLikeComment", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public Map<String, Integer> getMomentLikeComment(String uid) {

        Map<String, Integer> momentCountMap = new HashMap<>();
        List<MomentCountData> momentCountDataList = momentActivityDao.momentCount(uid, COUNT_TIME_START, COUNT_TIME_END);
        if (momentCountDataList != null && !momentCountDataList.isEmpty()) {
            MomentCountData momentCountData = momentCountDataList.get(0);
            momentCountMap.put("uidCount", momentCountData.getUidCount());
            momentCountMap.put("commentCount", momentCountData.getCommentCount());
            momentCountMap.put("likeCount", momentCountData.getLikeCount());
        } else {
            momentCountMap.put("uidCount", 0);
            momentCountMap.put("commentCount", 0);
            momentCountMap.put("likeCount", 0);
        }
        return momentCountMap;
    }


    @Cacheable(value = "getActivityMemoryData", key = "#p0 + #p1", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public ActivityMemoryData getActivityMemoryData(String uid, int memoryType) {
        if (memoryType == ActivityMemoryDao.MEMORY_TYPE_RECEIVE) {
            return activityMemoryDao.selectReceiveOne(uid, memoryType);
        } else {
            return activityMemoryDao.selectOne(uid, memoryType);
        }
    }


    public AnniversarySixVO memorySixthConfig(String uid) {

        AnniversarySixVO vo = new AnniversarySixVO();
        if (StringUtils.isEmpty(uid) || uid.length() != 24) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }


        Map<String, Integer> allAnniversaryMap = anniversaryRedis.getAnniversarySixAll(ACTIVITY_ID, uid);

        // 设置注册信息 (忠诚达人)
        int registerTime = new ObjectId(uid).getTimestamp();
        int registerDay = (COUNT_TIME_END - registerTime) / 86400;
        vo.setRegisterDate(DateHelper.ARABIAN.formatDateInDay(new Date(registerTime * 1000L)));
        vo.setRegisterDay(registerDay);
        Integer loyaltyStatus = allAnniversaryMap.get(LOYALTY);

        if (loyaltyStatus == null) {
            if (registerDay > 365) {
                distributionService.sendRewardResource(uid, LOYALTY_Id, ActivityRewardTypeEnum.getEnumByName("honor_title"), 30, 0, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, 0);
                anniversaryRedis.updateAnniversarySixHonorTitle(ACTIVITY_ID, uid, LOYALTY, 1);
                loyaltyStatus = 1;
            } else {
                anniversaryRedis.updateAnniversarySixHonorTitle(ACTIVITY_ID, uid, LOYALTY, 0);
                loyaltyStatus = 0;
            }
        }
        vo.setLoyalty(loyaltyStatus);


        // 设置在房时长(活跃达人)
        ActivityMemoryData activityMemoryData = anniversarySixService.getActivityMemoryData(uid, ActivityMemoryDao.MEMORY_TYPE_TIME);
        if (activityMemoryData != null) {
            vo.setActiveStart(activityMemoryData.getOnlineHour());
            vo.setActiveEnd(vo.getActiveStart() + 1);
            long lineMinute = activityMemoryData.getRoomOnline() / 60;
            vo.setChatTime(lineMinute);

            Integer activeStatus = allAnniversaryMap.get(ACTIVE);
            if (activeStatus == null) {
                if (vo.getChatTime() > 1000) {
                    distributionService.sendRewardResource(uid, ACTIVE_Id, ActivityRewardTypeEnum.getEnumByName("honor_title"), 30, 0, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, 0);
                    anniversaryRedis.updateAnniversarySixHonorTitle(ACTIVITY_ID, uid, ACTIVE, 1);
                    activeStatus = 1;
                } else {
                    anniversaryRedis.updateAnniversarySixHonorTitle(ACTIVITY_ID, uid, ACTIVE, 0);
                    activeStatus = 0;
                }
            }
            vo.setActive(activeStatus);
        }

        // 设置朋友圈数(社交达人)
        Map<String, Integer> momentCountMap = anniversarySixService.getMomentLikeComment(uid);
        vo.setMoment(momentCountMap.get("uidCount"));
        vo.setMomentComment(momentCountMap.get("commentCount"));
        vo.setMomentLike(momentCountMap.get("likeCount"));
        Integer socialStatus = allAnniversaryMap.get(SOCIAL);

        if (socialStatus == null) {
            if (vo.getMomentLike() > 500) {
                distributionService.sendRewardResource(uid, SOCIAL_Id, ActivityRewardTypeEnum.getEnumByName("honor_title"), 30, 0, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, 0);
                anniversaryRedis.updateAnniversarySixHonorTitle(ACTIVITY_ID, uid, SOCIAL, 1);
                socialStatus = 1;
            } else {
                anniversaryRedis.updateAnniversarySixHonorTitle(ACTIVITY_ID, uid, SOCIAL, 0);
                socialStatus = 0;
            }
        }
        vo.setSocial(socialStatus);

        // 设置我发给谁最多的top1(最佳友人)
        ActivityMemoryData sendMemoryData = anniversarySixService.getActivityMemoryData(uid, ActivityMemoryDao.MEMORY_TYPE_SEND);
        if (sendMemoryData != null) {
            ActorData toActor = actorDao.getActorDataFromCache(sendMemoryData.getToUid());
            if (toActor != null) {
                vo.setTopYouSendUid(toActor.getUid());
                vo.setTopYouSendHead(ImageUrlGenerator.generateRoomUserUrl(toActor.getHead()));
                vo.setTopYouSendName(toActor.getName());

                Integer bestFriendStatus = allAnniversaryMap.get(BEST_FRIEND);
                if (bestFriendStatus == null) {
                    if (sendMemoryData.getCostDiamond() > 100000) {
                        distributionService.sendRewardResource(uid, BEST_FRIEND_Id, ActivityRewardTypeEnum.getEnumByName("honor_title"), 30, 0, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, 0);
                        anniversaryRedis.updateAnniversarySixHonorTitle(ACTIVITY_ID, uid, BEST_FRIEND, 1);
                        bestFriendStatus = 1;
                    } else {
                        anniversaryRedis.updateAnniversarySixHonorTitle(ACTIVITY_ID, uid, BEST_FRIEND, 0);
                        bestFriendStatus = 0;
                    }
                }
                vo.setBestFriend(bestFriendStatus);
            }
        }

        // 设置谁发给我最多的top1(王牌红人)
        ActivityMemoryData receiveMemoryData = anniversarySixService.getActivityMemoryData(uid, ActivityMemoryDao.MEMORY_TYPE_RECEIVE);
        if (receiveMemoryData != null) {
            ActorData sendActor = actorDao.getActorDataFromCache(receiveMemoryData.getFromUid());
            if (sendActor != null) {
                vo.setTopSendToYouUid(sendActor.getUid());
                vo.setTopSendToYouHead(ImageUrlGenerator.generateRoomUserUrl(sendActor.getHead()));
                vo.setTopSendToYouName(sendActor.getName());

                Integer aceRedStatus = allAnniversaryMap.get(ACE_RED);
                if (aceRedStatus == null) {
                    if (receiveMemoryData.getCostDiamond() > 100000) {
                        distributionService.sendRewardResource(uid, ACE_RED_Id, ActivityRewardTypeEnum.getEnumByName("honor_title"), 30, 0, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, 0);
                        anniversaryRedis.updateAnniversarySixHonorTitle(ACTIVITY_ID, uid, ACE_RED, 1);
                        aceRedStatus = 1;
                    } else {
                        anniversaryRedis.updateAnniversarySixHonorTitle(ACTIVITY_ID, uid, ACE_RED, 0);
                        aceRedStatus = 0;
                    }
                }
                vo.setAceRed(aceRedStatus);
            }
        }

        return vo;
    }


    public void memorySixthShare(String uid, String picture, int slang, String momentText) {
        String activityId;
        String origin;
        if (!StringUtils.hasLength(momentText)) {
            momentText = slang == 1 ? ACTIVITY_TITLE_EN : ACTIVITY_TITLE_AR;
            activityId = ACTIVITY_ID;
            origin = ACTIVITY_ORIGIN;
        } else {
            activityId = BACK_ACTIVITY_ID;
            origin = BACK_ACTIVITY_ID;
        }
        Map<String, Integer> allAnniversaryMap = anniversaryRedis.getAnniversarySixAll(ACTIVITY_ID, uid);
        if (allAnniversaryMap.getOrDefault(ACTIVITY_SHARE_MOMENT, 0) > 0){
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE);
        }
        otherActivityService.commonMomentPush(uid, momentText, activityId, origin, picture, 800, 800);
        anniversaryRedis.updateAnniversarySixHonorTitle(ACTIVITY_ID, uid, ACTIVITY_SHARE_MOMENT, 1);
    }


    public AnniversaryStarVO anniversarySixthConfig(String uid, String activityId) {

        AnniversaryStarVO vo = new AnniversaryStarVO();
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        String roomId = otherActivityService.getPopularRoomId();
        Map<String, Integer> allTaskMap = anniversaryRedis.getAnniversaryTaskAll(activityId, uid);

        vo.setBagStatus(allTaskMap.getOrDefault(STAR_BAG_STATUS, 0));
        vo.setGiftList(activityData.getActivityGiftList());

        List<ActivityCommonConfig.AnniversarySixConfig> anniversarySixConfigList = activityCommonConfig.getAnniversarySixConfigList();
        for (ActivityCommonConfig.AnniversarySixConfig config : anniversarySixConfigList) {
            config.setRoomId(roomId);
            config.setCurrentProcess(Math.min(allTaskMap.getOrDefault(config.getStar(), 0), config.getTotalProcess()));
        }

        vo.setAnniversarySixConfigList(anniversarySixConfigList);

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();
        vo.setDeviceGet(anniversaryRedis.isGetBagGift(activityId, tnId));
        return vo;
    }


    public void distributionAnniversary(String uid) {
        Map<String, Integer> allTaskMap = anniversaryRedis.getAnniversaryTaskAll(ACTIVITY_ID_6TH, uid);
        int bagStatus = allTaskMap.getOrDefault(STAR_BAG_STATUS, 0);
        if (bagStatus > 0) {
            return;
        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null || StringUtils.isEmpty(actorData.getTn_id())) {
            logger.info("distributionAnniversary not find user or tn_id empty: {}", uid);
            return;
        }

        String tnId = actorData.getTn_id();
        if (anniversaryRedis.isGetBagGift(ACTIVITY_ID_6TH, tnId) >= 1) {
            logger.info("distributionAnniversary tn_id is get: {}", tnId);
            return;
        }

        List<ActivityCommonConfig.AnniversarySixConfig> anniversarySixConfigList = activityCommonConfig.getAnniversarySixConfigList();
        int totalFinish = 0;
        for (ActivityCommonConfig.AnniversarySixConfig config : anniversarySixConfigList) {
            int finishProcess = allTaskMap.getOrDefault(config.getStar(), 0);
            if (finishProcess >= config.getTotalProcess()) {
                totalFinish += 1;
            }
        }

        if (totalFinish >= 6) {
            distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 100, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, 0);
            distributionService.sendRewardResource(uid, BAG_GIFT1, ActivityRewardTypeEnum.getEnumByName("gift"), 15, 2, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, 0);
            distributionService.sendRewardResource(uid, BAG_GIFT2, ActivityRewardTypeEnum.getEnumByName("gift"), 15, 1, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, 0);
            distributionService.sendRewardResource(uid, BAG_ROOM_PICTURE, ActivityRewardTypeEnum.getEnumByName("background"), 7, 0, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, 0);
            distributionService.sendRewardResource(uid, BAG_FLOAT, ActivityRewardTypeEnum.getEnumByName("float_screen"), 7, 0, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, 0);
            anniversaryRedis.updateAnniversarySixTask(ACTIVITY_ID_6TH, uid, STAR_BAG_STATUS, 1);
            anniversaryRedis.addUserBagGift(ACTIVITY_ID_6TH, tnId);
        }
    }

    public void clickAnniversaryVideo(String uid, String activityId) {
        checkActivityTime(activityId);
        Map<String, Integer> allTaskMap = anniversaryRedis.getAnniversaryTaskAll(activityId, uid);
        int bagStatus = allTaskMap.getOrDefault(STAR_BAG_STATUS, 0);
        Integer guardNum = allTaskMap.get(STAR_GUARD);
        if (bagStatus <= 0 && guardNum == null) {
            anniversaryRedis.updateAnniversarySixTask(activityId, uid, STAR_GUARD, 1);
            distributionAnniversary(uid);
        }
    }

    public void handleAnniversaryTask(SendGiftData giftData, String activityId) {

        try {
            int totalSendBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            int receiveBeans = giftData.getNumber() * giftData.getPrice();
            String fromUid = giftData.getFrom_uid();

            Map<String, Integer> allFromTaskMap = anniversaryRedis.getAnniversaryTaskAll(activityId, fromUid);
            int supportTask = allFromTaskMap.getOrDefault(STAR_SUPPORT, 0);
            int bagStatus = allFromTaskMap.getOrDefault(STAR_BAG_STATUS, 0);
            if (bagStatus <= 0 && supportTask < STAR_BEAN_LIMIT) {
                int afterNum = supportTask + totalSendBeans;
                anniversaryRedis.updateAnniversarySixTask(activityId, fromUid, STAR_SUPPORT, afterNum);
                distributionAnniversary(fromUid);
            }

            for (String aid : giftData.getAid_list()) {
                Map<String, Integer> allToTaskMap = anniversaryRedis.getAnniversaryTaskAll(activityId, aid);
                int accompanyTask = allToTaskMap.getOrDefault(STAR_ACCOMPANY, 0);
                int aidBagStatus = allToTaskMap.getOrDefault(STAR_BAG_STATUS, 0);
                if (aidBagStatus <= 0 && accompanyTask < STAR_BEAN_LIMIT) {
                    int afterAidNum = accompanyTask + receiveBeans;
                    anniversaryRedis.updateAnniversarySixTask(activityId, aid, STAR_ACCOMPANY, afterAidNum);
                    distributionAnniversary(aid);
                }
            }
        } catch (Exception e) {
            logger.error("handleAnniversaryTask: {}", e.getMessage(), e);
        }
    }


    public void handleAnniversaryUserRoomTask(EnterRoomMqData enterRoomMqData) {

        try {

            String uid = enterRoomMqData.getUid();
            String roomId = enterRoomMqData.getRoomId();
            String item = enterRoomMqData.getItem();
            int num = enterRoomMqData.getNum();

            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID_6TH);
            if (activityData == null) {
                return;
            }

            if (activityData.getAcNameEn().startsWith("test") && !MqTopicItemConstant.UP_MIC.equals(item) && !whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                return;
            }

            if (activityData.getAcNameEn().startsWith("test") && MqTopicItemConstant.UP_MIC.equals(item) && !whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID)) {
                return;
            }

            int curTime = DateHelper.getNowSeconds();
            int startTime = activityData.getStartTime();
            int endTime = activityData.getEndTime();

            if (curTime < startTime || curTime > endTime) {
                return;
            }

            if (MqTopicItemConstant.ENTER_ROOM.equals(item) && !StringUtils.isEmpty(roomId)) {
                Map<String, Integer> allFromTaskMap = anniversaryRedis.getAnniversaryTaskAll(ACTIVITY_ID_6TH, uid);
                int listenTask = allFromTaskMap.getOrDefault(STAR_LISTEN, 0);
                int bagStatus = allFromTaskMap.getOrDefault(STAR_BAG_STATUS, 0);
                if (bagStatus <= 0 && listenTask < STAR_LISTEN_LIMIT) {

                    long flag = anniversaryRedis.addUserRoomTask(ACTIVITY_ID_6TH, uid, roomId);
                    if (flag > 0) {
                        int afterNum = listenTask + 1;
                        anniversaryRedis.updateAnniversarySixTask(ACTIVITY_ID_6TH, uid, STAR_LISTEN, afterNum);
                        distributionAnniversary(uid);
                    }
                }
            }


            if (MqTopicItemConstant.ALL_BAI_SHUN_LIST.contains(item) && !StringUtils.isEmpty(roomId)) {
                Map<String, Integer> allFromTaskMap = anniversaryRedis.getAnniversaryTaskAll(ACTIVITY_ID_6TH, uid);
                int happyTask = allFromTaskMap.getOrDefault(STAR_HAPPY, 0);
                int bagStatus = allFromTaskMap.getOrDefault(STAR_BAG_STATUS, 0);
                if (bagStatus <= 0 && happyTask < STAR_HAPPY_LIMIT) {

                    long flag = anniversaryRedis.addUserPlayGameTask(ACTIVITY_ID_6TH, uid, item);
                    if (flag > 0) {
                        int afterHappyNum = happyTask + 1;
                        anniversaryRedis.updateAnniversarySixTask(ACTIVITY_ID_6TH, uid, STAR_HAPPY, afterHappyNum);
                        distributionAnniversary(uid);
                    }
                }
            }


            if (MqTopicItemConstant.UP_MIC.equals(item)) {
                Map<String, Integer> allFromTaskMap = anniversaryRedis.getAnniversaryTaskAll(ACTIVITY_ID_6TH, uid);
                int talkTask = allFromTaskMap.getOrDefault(STAR_TALK, 0);
                int bagStatus = allFromTaskMap.getOrDefault(STAR_BAG_STATUS, 0);
                if (bagStatus <= 0 && talkTask < STAR_TALK_LIMIT) {
                    int afterTalkNum = talkTask + num;
                    anniversaryRedis.updateAnniversarySixTask(ACTIVITY_ID_6TH, uid, STAR_TALK, afterTalkNum);
                    distributionAnniversary(uid);
                }
            }

        } catch (Exception e) {
            logger.error("handleAnniversaryUserRoomTask: {}", e.getMessage(), e);
        }
    }
}
